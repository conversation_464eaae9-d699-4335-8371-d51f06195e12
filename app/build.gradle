import com.bukuwarung.buildsrc.Libs
import groovy.json.JsonSlurper

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-kapt'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: "androidx.navigation.safeargs.kotlin"
apply plugin: 'com.google.firebase.firebase-perf'
apply plugin: 'dagger.hilt.android.plugin'

def readEnv() {
    final taskNames = gradle.startParameter.taskNames.join().toLowerCase()
    if (taskNames.contains("stg")) {
        return "stg"
    } else if (taskNames.contains("prod")) {
        return "prod"
    } else {
        return "dev"
    }
}

def readKeys() {
    final json = new JsonSlurper()
    final env = readEnv()
    final file = file("../keys-${env}.json").text
    return json.parseText(file).toMapString()
}

android {
    aaptOptions {
        noCompress "webp"
    }
    buildFeatures {
        viewBinding true
    }
    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
        unitTests.all {
            systemProperty 'robolectric.dependency.repo.url', 'https://repo1.maven.org/maven2'
        }
    }
    compileSdkVersion buildConfig.compileSdk
    testOptions {
        animationsDisabled = true
    }

    dataBinding {
        enabled = true
    }

    defaultConfig {
        multiDexEnabled true

        applicationId "com.bukuwarung"
        minSdkVersion buildConfig.minSdk
        targetSdkVersion buildConfig.targetSdk
        versionCode buildConfig.versionCode
        versionName buildConfig.versionName
        testInstrumentationRunner "com.bukuwarung.base.runner.HiltTestRunner"

        resConfigs "en_US", "id_ID"

        //TODO :TEST REMOVING IT
        vectorDrawables.useSupportLibrary = true

        buildConfigField("String", "PLAYSTORE_URL", '"https://play.google.com/store/apps/details?id=com.bukuwarung"')

        javaCompileOptions {
            annotationProcessorOptions {
                arguments["dagger.hilt.disableModulesHaveInstallInCheck"] = "true"
                arguments["stealth.keys"] = readKeys()
            }
        }
    }
    buildTypes {
        debug {
            minifyEnabled false
            resValue "string", "clear_text_config", "true"
        }
        release {
            minifyEnabled true
            shrinkResources true
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "clear_text_config", "false"
        }
    }
    flavorDimensions "default"
    productFlavors {
        dev {
            applicationIdSuffix ".dev"
            versionNameSuffix "-DEV"

            def contentProviderAuthority = defaultConfig.applicationId + applicationIdSuffix + ".stickercontentprovider"
            manifestPlaceholders += [
                    contentProviderAuthority: contentProviderAuthority,
                    deeplinkUrl: 'bukuwarungdev.page.link',
                    fileProviderAuthority: 'com.bukuwarung.drod.dev',
                    fbDeepLinkUrl: 'bukuwarung',
                    CLEVERTAP_ACCOUNT_ID: 'TEST-RKW-RZW-4K6Z',
                    CLEVERTAP_TOKEN: 'TEST-012-032',
                    FCM_SENDER_ID:'id:*************',
                    MAPS_SDK_KEY:'AIzaSyBeIRIzqGU32sX-dJJD7iFxExu82kW5mo4'
            ]

            buildConfigField("String", "fileProviderAuthority", '"com.bukuwarung.drod.dev"')
            buildConfigField("String", "MOEAPI", '"79FTM8WDYZWGX5BM3X6DXUPH"')
            buildConfigField("String", "AF_API", '"w5qqbe8GwcDGReGgqpRsRW"')
            buildConfigField("String", "AF_API_TOKEN", '"d7f4dd13-a5d4-4ff7-964c-2c8358f5f952"')
            buildConfigField("String", "AF_IMPORT_KEY", '"8769159b-c6e6-4e4c-a3ec-64eaba43a277"')
            buildConfigField("String", "AF_APP_ID", '"com.bukuwarung"')
            buildConfigField("String", "CONTENT_PROVIDER_AUTHORITY", "\"${contentProviderAuthority}\"")
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarungdev.page.link"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.17.0"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarungdev.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarungdev.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/p?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-dev.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY",'"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-dev.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-dev.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-dev.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-dev.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-dev.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://2yvdmltuje.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-dev.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/kyckyb"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-dev.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-dev.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-dev.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-dev.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-dev.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-dev.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-dev.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-dev.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-dev.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-dev.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("String", "S3_BUCKET", '"https://bukuwarungac-image-dev.s3.ap-southeast-1.amazonaws.com"')
            buildConfigField("boolean", "MULTI_LANGUAGE_ENABLED", "true")
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "BUKUAGEN_PACKAGE_NAME", '"com.bukuwarung.bukuagen.dev"')
            buildConfigField("String", "HMAC_SECRET", '"test-secret"')
        }
        stg {
            applicationIdSuffix ".staging"
            versionNameSuffix "-STG"

            def contentProviderAuthority = defaultConfig.applicationId+ applicationIdSuffix + ".stickercontentprovider"
            manifestPlaceholders += [
                    contentProviderAuthority: contentProviderAuthority,
                    deeplinkUrl: 'bukuwarungstg.page.link',
                    fileProviderAuthority: 'com.bukuwarung.drod.staging',
                    fbDeepLinkUrl: 'bukuwarung',
                    CLEVERTAP_ACCOUNT_ID: 'TEST-WR5-78Z-846Z',
                    CLEVERTAP_TOKEN: 'TEST-1ba-520',
                    FCM_SENDER_ID:'id:************',
                    MAPS_SDK_KEY:'AIzaSyBeIRIzqGU32sX-dJJD7iFxExu82kW5mo4'
            ]
            buildConfigField("String", "MOEAPI", '"GRAFCYG32R1FS54ME39HGO8S"')
            buildConfigField("String", "AF_API", '"w5qqbe8GwcDGReGgqpRsRW"')
            buildConfigField("String", "AF_API_TOKEN", '"d7f4dd13-a5d4-4ff7-964c-2c8358f5f952"')
            buildConfigField("String", "AF_IMPORT_KEY", '"8769159b-c6e6-4e4c-a3ec-64eaba43a277"')
            buildConfigField("String", "AF_APP_ID", '"com.bukuwarung"')
            buildConfigField("String", "fileProviderAuthority", '"com.bukuwarung.drod.staging"')
            buildConfigField("String", "CONTENT_PROVIDER_AUTHORITY", "\"${contentProviderAuthority}\"")
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarungstg.page.link"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarungstg.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarungstg.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/y?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-staging-v1.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-staging-v1.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-staging-v1.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-staging-v1.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-staging-v1.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-staging-v1.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.17.0"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://xsvg63im27.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/kyckyb"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-staging-v1.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-staging-v1.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-staging-v1.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("boolean", "MULTI_LANGUAGE_ENABLED", "true")
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "BUKUAGEN_PACKAGE_NAME", '"com.bukuwarung.bukuagen.staging"')
            buildConfigField("String", "HMAC_SECRET", '"3a4cc7f4467c6cace7a23fb540eb07e82e20e476f2f0abd93353284e98bbc95f"')
        }
        prod {
            def contentProviderAuthority = defaultConfig.applicationId + ".stickercontentprovider"
            manifestPlaceholders += [
                    contentProviderAuthority: contentProviderAuthority,
                    deeplinkUrl: 'bukuwarung.page.link',
                    fileProviderAuthority: 'com.bukuwarung.drod',
                    fbDeepLinkUrl: 'bukuwarung',
                    CLEVERTAP_ACCOUNT_ID: '6K5-K78-9R6Z',
                    CLEVERTAP_TOKEN: 'ba3-536',
                    FCM_SENDER_ID:'id:************',
                    MAPS_SDK_KEY:'AIzaSyCtZTXmvBGj0M4F_F_WDNzucA6_SMSoo04'
            ]
            buildConfigField("String", "MOEAPI", '"GRAFCYG32R1FS54ME39HGO8S"')
            buildConfigField("String", "AF_API", '"w5qqbe8GwcDGReGgqpRsRW"')
            buildConfigField("String", "AF_API_TOKEN", '"d7f4dd13-a5d4-4ff7-964c-2c8358f5f952"')
            buildConfigField("String", "AF_IMPORT_KEY", '"8769159b-c6e6-4e4c-a3ec-64eaba43a277"')
            buildConfigField("String", "AF_APP_ID", '"com.bukuwarung"')
            buildConfigField("String", "fileProviderAuthority", '"com.bukuwarung.drod"')
            buildConfigField("String", "CONTENT_PROVIDER_AUTHORITY", "\"${contentProviderAuthority}\"")
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarung.page.link"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarung.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarung.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/y?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-v4.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-v4.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-v4.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-v4.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-v4.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-v4.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.60.0-bud"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://xsvg63im27.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-v4.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/kyckyb"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-v4.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-v4.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-v4.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-v4.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-v4.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-v4.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-v4.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-v4.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-v4.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-v4.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("boolean", "MULTI_LANGUAGE_ENABLED", "false")
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "BUKUAGEN_PACKAGE_NAME", '"com.bukuwarung.bukuagen"')
            buildConfigField("String", "HMAC_SECRET", '"1cb25c8679c149f41a54148c71d0f35a1125c4954e9efb8485b4b045b5f8d954"')
        }
    }
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }

    compileOptions {
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled = true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        freeCompilerArgs = [
            "-Xopt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            "-Xopt-in=kotlinx.coroutines.FlowPreview",
            "-Xopt-in=kotlinx.coroutines.InternalCoroutinesApi",
        ]

        jvmTarget = JavaVersion.VERSION_1_8.toString()
    }

    packagingOptions {
        exclude 'lib/*/libnative-imagetranscoder.so'
        exclude 'lib/*/libnative-filters.so'
        exclude 'META-INF/gradle/incremental.annotation.processors'
    }
    namespace 'com.bukuwarung'

    //tmp disable AndroidTest due to mismatch with the UI
    tasks.whenTaskAdded { task ->
        if (task.name.contains("AndroidTest")) {
            task.enabled = false
        }
    }
}

dependencies {

//    implementation fileTree(include: ['*.jar'], dir: 'libs')
    // use aar for webview
    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')

//     use project for webview
//    implementation project(':android-webview')
    implementation project(':base-android')
    implementation project(':neuro')
    implementation project(':privypass')
    implementation project(':ui-component')
    implementation project(':buku-bluetooth-printer')
    implementation project(':stealth')
    implementation project(':stealth-compiler')

    implementation Libs.JetBrains.Kotlin.stdLibJDK7
    implementation Libs.ThirdParty.mixPanelAndroid

    implementation Libs.AndroidX.constraintLayout
    implementation Libs.AndroidX.multidex
    implementation Libs.AndroidX.recyclerView
    implementation Libs.AndroidX.cardView
    implementation Libs.AndroidX.legacySupport
    implementation Libs.AndroidX.activityKtx
    implementation Libs.AndroidX.coreKtx

    implementation Libs.AndroidX.Navigation.navigationFragmentKtx
    implementation Libs.AndroidX.Navigation.navigationUIKtx
    implementation Libs.AndroidX.appCompat
    implementation Libs.JetBrains.Kotlin.stdLib

    //testing
    androidTestImplementation Libs.AndroidX.Test.runner
    androidTestImplementation Libs.AndroidX.Test.espresso
    androidTestImplementation Libs.AndroidX.Test.espressoContrib
    androidTestImplementation Libs.AndroidX.Test.rules
    androidTestImplementation Libs.AndroidX.Test.uiautomator
    androidTestImplementation Libs.Testing.coroutinesTest
    androidTestImplementation Libs.Testing.coreTesting
    androidTestImplementation Libs.Testing.coreKtxTesting
    testImplementation Libs.Testing.mockK
    testImplementation Libs.Testing.robolectric
    testImplementation Libs.Testing.jUnit
    testImplementation Libs.Testing.mokito
    testImplementation Libs.Testing.mokitoCore
    testImplementation Libs.Testing.coreTesting
    testImplementation Libs.Testing.coroutinesTest
    implementation Libs.AndroidX.Fragment.fragmentKtx
    implementation Libs.AndroidX.Jetpack.workmanagerRuntime
    implementation Libs.AndroidX.Hilt.hiltWork
    implementation Libs.AndroidX.Lifecycle.extensions
    implementation Libs.AndroidX.Lifecycle.liveDataCore
    implementation Libs.AndroidX.Lifecycle.liveDataKtx
    implementation Libs.AndroidX.Lifecycle.runtime
    annotationProcessor Libs.AndroidX.Lifecycle.compiler

    implementation Libs.AndroidX.Room.runtime
    annotationProcessor Libs.AndroidX.Room.compiler
    kapt Libs.AndroidX.Room.compiler
    implementation Libs.AndroidX.Room.coroutine
    implementation Libs.AndroidX.Room.roomRxJava

    implementation Libs.Google.material
    implementation Libs.Google.PlayServices.location
    implementation Libs.Google.PlayServices.authApiPhone
    implementation Libs.Google.PlayServices.base
    implementation Libs.Google.PlayServices.identity
    implementation Libs.Google.PlayServices.auth
    implementation Libs.Google.PlayServices.task
    implementation Libs.Google.PlayServices.appUpdate

    implementation platform(Libs.Google.Firebase.platform)
    implementation Libs.Google.Firebase.messaging
    implementation Libs.Google.Firebase.auth
    implementation Libs.Google.Firebase.firestore
    implementation Libs.Google.Firebase.dynamicLinks
    implementation Libs.Google.Firebase.analytics
    implementation Libs.Google.Firebase.storage
    implementation Libs.Google.Firebase.remoteConfig
    implementation Libs.Google.material

    implementation Libs.Google.gson

    implementation Libs.Android.volley
    implementation Libs.Android.installReferrer

    implementation Libs.Facebook.androidSdk
    implementation Libs.Facebook.shimmer

    implementation Libs.RxJava.rxKotlin
    implementation Libs.RxJava.rxJava2

    implementation Libs.SquareUp.Retrofit.retrofit
    implementation Libs.SquareUp.Retrofit.gsonConverter
    implementation Libs.SquareUp.Retrofit.okHttp
    implementation Libs.SquareUp.Retrofit.loggingInterceptor

    implementation Libs.Analytics.amplitude
    implementation Libs.Analytics.appsflyer
    implementation Libs.Google.Firebase.crashlytics
    implementation Libs.AndroidX.Pagination.paginationLibrary

    implementation Libs.Glide.glide
    annotationProcessor Libs.Glide.compiler

    implementation Libs.ThirdParty.countryCodePicker
    implementation Libs.ThirdParty.colorPicker
    implementation Libs.ThirdParty.simpleTooltip
    implementation Libs.ThirdParty.lottie
    implementation Libs.ThirdParty.powerspinner
    implementation Libs.ThirdParty.keyboardEvent
    implementation Libs.ThirdParty.dotsIndicator

    implementation (Libs.ThirdParty.zxingAndroid) { transitive = false }
    implementation Libs.ThirdParty.zxingCore

    implementation Libs.ThirdParty.barGraph

    implementation Libs.ThirdParty.progressbutton

    // webview dependencies
    implementation Libs.AndroidX.Camera.camera2
    implementation Libs.AndroidX.Camera.lifecycle
    implementation Libs.AndroidX.Camera.cameraView
    implementation Libs.AndroidX.Camera.cameraVidio
    implementation Libs.JetBrains.Coroutine.core
    implementation Libs.JetBrains.Coroutine.android
    implementation Libs.ThirdParty.zeloryCompressor
    implementation Libs.Google.mlKitFaceDetection
    implementation Libs.SquareUp.Retrofit.adapterRxjava2
    implementation Libs.RxJava.rxJava2RxJava

    implementation Libs.ThirdParty.kal72RackMonthPicker
    implementation Libs.ThirdParty.dewinjmMonthyearPicker
    implementation Libs.ThirdParty.mrmikeok2curl

    implementation Libs.Google.PlayServices.maps
    implementation Libs.Google.Exoplayer.exoplayer
    implementation Libs.Google.Exoplayer.exoplayerHls
    implementation Libs.Google.Exoplayer.exoplayerUi

    implementation Libs.OAuth.oauthJWT

    // performance
    implementation Libs.Google.Firebase.perf
    implementation Libs.Google.Firebase.perfKtx


    implementation Libs.ThirdParty.salesiqMobilisten

    //desugaring
    coreLibraryDesugaring(Libs.Android.desugarJdkLibs)

    implementation Libs.Dagger.main
    implementation Libs.Dagger.mainAndroid
    implementation Libs.Dagger.androidSupport
    kapt Libs.Dagger.processor
    kapt Libs.Dagger.compiler

    // Dagger - Hilt
    implementation Libs.AndroidX.Hilt.hilt
    kapt Libs.AndroidX.Hilt.hiltCompiler

    androidTestImplementation Libs.AndroidX.Hilt.hiltTesting
    kaptAndroidTest Libs.AndroidX.Hilt.hiltCompiler

    implementation Libs.ThirdParty.tooLargeTool
    implementation(Libs.ThirdParty.survicateSdk) {
        exclude group: 'com.google.android.material', module: 'material'
    }


    // Bureau
    implementation Libs.ThirdParty.deviceIntelligenceSdk
}


//This needed to uncomment when webview project added
configurations.all {
    resolutionStrategy {
        force 'androidx.core:core:1.8.0'
    }
}
