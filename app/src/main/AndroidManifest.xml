<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:compileSdkVersion="28"
    android:compileSdkVersionCodename="1"
    android:versionCode="3399"
    android:versionName="3.5.0">

    <uses-sdk tools:overrideLibrary="com.google.zxing.client.android" />
    <!-- USER PERMISSIONS -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
        tools:ignore="ProtectedPermissions"/>
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>

    <permission
        android:name="${applicationId}.permission.MIPUSH_RECEIVE"
        android:protectionLevel="signature" />

    <uses-permission android:name="${applicationId}.permission.MIPUSH_RECEIVE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="false" />

    <queries>
        <package android:name="com.whatsapp" />
        <package android:name="com.whatsapp.w4b" />
    </queries>
    <queries>
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
    </queries>

    <application
        android:name=".Application"
        android:allowBackup="false"
        android:fullBackupOnly="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/BukuTheme"
        tools:ignore="AllowBackup"
        tools:replace="android:allowBackup">

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="${MAPS_SDK_KEY}" />

        <activity
            android:name=".activities.geolocation.view.MapsActivity"
            android:exported="false"/>
        <activity
            android:name=".activities.supplier.AddSupplierActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.print.setup.BluetoothPrinterScanActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme" />
        <activity android:name=".feature.login.createPassword.screen.ForgotPasswordActivity"
            android:exported="false"
            />
        <activity android:name=".feature.login.createPassword.screen.ChangePasswordActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            />
        <activity
            android:name=".feature.login.password.screen.PasswordActivity"
            android:exported="false" />
        <activity
            android:name=".feature.login.createPassword.screen.CreateNewPasswordActivity"
            android:exported="false" />
        <activity
            android:name=".activities.expense.category.CategoryDescriptionActivity"
            android:exported="false" />
        <activity
            android:name=".activities.productcategory.view.CategoryAssociatorActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.productcategory.view.ProductCategoryActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.print.NotesMissionActivity"
            android:exported="false" />
        <activity
            android:name=".activities.autorecordtransaction.FinancialInformationActivity"
            android:exported="false" />
        <activity
            android:name=".activities.geolocation.view.BusinessAddressActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activities.catalogproduct.view.CatalogProductActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.dailyupdatestates.DailyUpdateWebviewActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity" />
        <activity
            android:name=".activities.experiments.CustomWebviewActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.profile.update.dialogs.UserProfileActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activities.profile.businessprofile.BusinessProfileNativeActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activities.profile.businessprofile.NgBusinessProfileActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activities.profile.businessprofile.BusinessOperationalInformationActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activities.profile.businessprofile.CreateBusinessProfileActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activities.profile.businessprofile.BusinessProfileWebviewActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.referral.leaderboard.LeaderboardWebviewActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity" />
        <activity
            android:name=".activities.dailyupdatestates.DailyUpdateNoTxnActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity" />
        <activity
            android:name=".activities.inventory.detail.EditStockActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.card.newcard.NewBusinessCardActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.pos.PosActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activities.invoice.InvoiceSettingActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.marketing.MarketingActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan|adjustResize" />
        <activity
            android:name=".activities.expense.ProductListActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan|adjustResize" />
        <activity
            android:name=".activities.print.setup.SetupPrinterActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".lib.webview.camera.CameraKycActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".lib.webview.camera.CameraKycV2Activity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.SplashActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.home.MainActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/BukuTheme"
            android:windowSoftInputMode="stateHidden">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:host="${deeplinkUrl}"
                    android:scheme="https" />
            </intent-filter>
            <intent-filter  android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="https"
                    android:host="bukuwarung.onelink.me"
                    android:pathPrefix="/dbmG" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:host="launch"
                    android:scheme="bukuwarung" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="bukutesturlscheme" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.onboarding.LoginActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.onboarding.NewLoginActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.onboarding.VerifyOtpActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.onboarding.NewVerifyOtpActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.business.CreateBusinessActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.navigation.SideNavActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.card.BusinessCardActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.transaction.category.CategoryTransactionsActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan|adjustResize" />
        <activity
            android:name=".activities.addcustomer.detail.AddCustomerActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.addcustomer.detail.CustomerActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.transaction.customer.add.AddTransactionActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan|adjustResize" />
        <activity
            android:name=".activities.customerprofile.CustomerDetailActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan|adjustResize" />
        <activity
            android:name=".activities.categorydetail.CashDetailActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan|adjustResize" />
        <activity
            android:name=".activities.expense.NewCashTransactionActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateHidden|adjustPan" />
        <activity
            android:name=".feature.transaction.record.screen.TransactionRecordActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".feature.transaction.inventory.screen.TransactionProductInventoryActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activities.inventory.detail.InventoryHistoryDetailActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activities.expense.detail.CashTransactionDetailActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activities.customer.transactiondetail.CustomerTransactionDetailActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".activities.transaction.customer.CustomerTransactionActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateAlwaysHidden" /> <!-- <activity -->
        <!-- android:name=".activities.onboarding.WelcomeActivity" -->
        <!-- android:theme="@style/NoActionBarAppTheme" /> -->
        <activity
            android:name=".activities.transaction.customer.LunaskanSuccessMessageActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activities.transactionreport.TransactionReportActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".collectingcalendar.main.CollectingCalendarActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".collectingcalendar.addcollectingdate.AddCollectingDateActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activities.appinfo.PrivacyActivity"
            android:configChanges="locale"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:replace="android:theme" />
        <activity
            android:name=".activities.profile.update.BusinessProfileFormActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activities.settings.SettingsActivity"
            android:configChanges="locale"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:replace="android:theme" />
        <activity
            android:name=".activities.HelpCenterActivity"
            android:configChanges="locale"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:replace="android:theme" />
        <activity
            android:name=".activities.referral.ShareApp"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:replace="android:theme" />
        <activity
            android:name=".activities.referral.payment_referral.ReferralActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:replace="android:theme" />
        <activity
            android:name=".activities.referral.main_referral.MainReferralActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:replace="android:theme" />
        <activity
            android:name=".activities.referral.leaderboard.LeaderboardActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:replace="android:theme" />
        <activity
            android:name=".activities.referral.share.UploadReferralImageActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:replace="android:theme" />
        <activity
            android:name=".activities.referral.history.ReferralHistoryActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:replace="android:theme" />
        <activity
            android:name=".activities.WebviewActivity"
            android:configChanges="locale"
            android:parentActivityName=".activities.home.MainActivity"
            android:screenOrientation="portrait"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:replace="android:theme" />

        <activity
            android:name=".activities.vida.VidaWebViewActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/NoActionBarAppTheme"
            android:windowSoftInputMode="stateHidden|adjustNothing" />

        <activity
            android:name=".lib.webview.SimpleWebViewActivity"
            android:configChanges="locale"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:replace="android:theme" />
        <activity
            android:name=".activities.notification.NotificationActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.selfreminder.SelfReminderActivity"
            android:label="@string/title_activity_self_remainder"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.selfreminder.SetSelfReminderActivity"
            android:label="@string/title_activity_set_self_remainder"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.stickers.StickerMainActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.stickers.StickerPackListActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.stickers.StickerPackDetailsActivity"
            android:parentActivityName=".activities.stickers.StickerPackListActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:ignore="UnusedAttribute">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".activities.stickers.StickerPackListActivity" />
        </activity>
        <activity
            android:name=".activities.successmessage.ProfileSuccessMessageActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".payments.CustomerListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.PaymentsActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.history.OrderHistoryActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name="com.bukuwarung.payments.pin.NewPaymentPinActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:theme="@style/NoActionBarAppTheme" />

        <activity
            android:name=".payments.selectbank.SelectBankActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.banklist.BankAccountListActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.addbank.AddBankAccountActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.checkout.PaymentCategoryActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateVisible" />
        <activity
            android:name=".payments.checkout.PaymentCheckoutActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateVisible" />
        <activity
            android:name=".payments.core.view.PaymentOutActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateVisible" />
        <activity
            android:name=".payments.core.view.PaymentInputPageActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateVisible" />
        <activity
            android:name=".payments.core.view.PaymentConfirmationPageActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".payments.core.view.PaymentStatusActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateVisible" />
        <activity
            android:name="com.bukuwarung.payments.ppob.confirmation.view.PpobStatusActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateVisible" />
        <activity
            android:name=".payments.core.view.SearchBankAccountActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateVisible" />
        <activity
            android:name=".payments.saldo.TopupSaldoActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="adjustResize|stateVisible" />

        <activity
            android:name=".payments.ppob.confirmation.view.PpobOrderFormActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".payments.PaymentHistoryDetailsActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.PaymentContactActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.ppob.reminders.view.ReminderActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.ppob.catalog.view.CatalogActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.ppob.catalog.view.PromotionActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.qris.QrisActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.AssistPageActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.ppob.base.view.PpobActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".payments.ppob.base.view.EwalletBillersActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activities.stickers.StickerPackInfoActivity"
            android:parentActivityName=".activities.stickers.StickerPackDetailsActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            tools:ignore="UnusedAttribute">
            <meta-data
                android:name="android.support.PARENT_ACTIVITY"
                android:value=".activities.stickers.StickerPackDetailsActivity" />
        </activity>
        <activity
            android:name=".payments.ppob.train.view.TrainTicketWebviewActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".payments.ppob.train.view.TrainTicketDetailsActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:process=":error_handler"
            android:name="com.bukuwarung.BukuCrashingActivity"
            android:exported="true" />
        <activity android:name=".activities.maintainance.MaintenanceActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:theme="@style/NoActionBarAppTheme"
            android:exported="false"
            android:windowSoftInputMode="stateHidden" />

        <activity
            android:name=".activities.kycupgrade.KycUpgradeActivity"
            android:screenOrientation="portrait"
            android:parentActivityName=".activities.homepage.view.HomepageNudgeActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />

        <provider
            android:name=".StickerContentProvider"
            android:authorities="${contentProviderAuthority}"
            android:enabled="true"
            android:exported="true"
            android:readPermission="com.whatsapp.sticker.READ" />

        <meta-data
            android:name="CLEVERTAP_REGION"
            android:value="sg1" />
        <meta-data
            android:name="CLEVERTAP_BACKGROUND_SYNC"
            android:value="1" />
        <meta-data
            android:name="com.facebook.accountkit.ApplicationName"
            android:value="@string/app_name" />
        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/FACEBOOK_APP_ID" />
        <meta-data
            android:name="com.survicate.surveys.workspaceKey"
            android:value="@string/SURVICATE_WORKSPACE_ID"/>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${fileProviderAuthority}"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepath" />
        </provider>

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="${applicationId}.firebaseinitprovider"
            android:exported="false"
            tools:node="remove" />
        <provider
            android:name=".StartupTimeProvider"
            android:authorities="${applicationId}.startuptimeprovider"
            android:exported="false"
            android:initOrder="**********" />

        <provider
            android:name=".FirebaseConfigProvider"
            android:authorities="${applicationId}.firebaseconfigprovider"
            android:exported="false"
            android:initOrder="**********" />
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            android:exported="false"
            tools:node="merge">
            <!-- If you are using androidx.startup to initialize other components -->
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
        </provider>

        <receiver
            android:name=".activities.referral.main_referral.ReferralSharingReceiver"
            android:exported="false" />
        <receiver
            android:name=".activities.referral.share.ReferralUploadReceiver"
            android:exported="false" />
        <receiver
            android:name=".utils.SharingUtilReceiver"
            android:exported="false" />
        <receiver
            android:name=".managers.local_notification.domain.DailyDueDateNotificationReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".managers.BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".activities.selfreminder.SelfReminderNotificationReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name=".payments.PaymentNotificationReceiver"
            android:enabled="true"
            android:exported="false" />

        <receiver
            android:name=".activities.EventLoggingReceiver"
            android:enabled="true"
            android:exported="true" />

        <receiver
            android:name=".activities.ExceptionLoggingReceiver"
            android:enabled="true"
            android:exported="true" />

        <receiver
            android:name=".activities.UrlReceiver"
            android:enabled="true"
            android:exported="true" />

        <activity
            android:name=".inventory.ui.product.AddProductActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/Theme.AppCompat.Translucent"
            android:windowSoftInputMode="adjustResize|stateVisible" />
        <activity
            android:name=".activities.card.BusinessCardShareActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.customer.transactiondetail.TransactionDetailExpandedActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity android:name=".activities.onboarding.WelcomeActivity" />
        <activity
            android:name=".activities.bulktransaction.view.BulkTransactionActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activities.expense.category.SelectCategory"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.expense.category.SelectCategoryInfo"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".inventory.ui.InventoryActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.businessdashboard.view.BusinessDashboardMainActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name=".activities.businessdashboard.view.BusinessDashboardWelcomeActivity"
            android:exported="false"
            android:parentActivityName=".activities.home.MainActivity" />
        <activity
            android:name=".activities.onboarding.form.OnBoardingFormActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".feature.onboarding.form.screen.OnBoardingFormActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".feature.onboarding.form.newScreen.NewOnBoardingFormActivity"
            android:configChanges="orientation|keyboardHidden|screenSize|screenLayout"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activities.livelinesscheck.CameraLivelinessActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activities.livelinesscheck.LivelinessLandingActivity"
            android:parentActivityName=".activities.home.MainActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".lib.webview.camera.VideoKycActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity
            android:name="com.bukuwarung.lib.webview.kyc.VideoKycPreviewActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity android:name=".activities.homepage.view.HomepageNudgeActivity"
            android:screenOrientation="portrait"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme" />
        <activity android:name=".activities.edc.cardhistory.ui.CardTransactionHistoryActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme"/>
        <activity android:name=".activities.edc.orderdetail.ui.EdcOrderDetailsActivity"
            android:exported="false"
            android:theme="@style/NoActionBarAppTheme"/>


        <receiver
            android:name="com.appsflyer.MultipleInstallBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.vending.INSTALL_REFERRER" />
            </intent-filter>
        </receiver>

        <service
            android:name=".analytics.ClevertapMessageListener"
            android:exported="false"
            android:enabled="true">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <meta-data
            android:name="CLEVERTAP_ACCOUNT_ID"
            android:value="${CLEVERTAP_ACCOUNT_ID}" />
        <meta-data
            android:name="CLEVERTAP_TOKEN"
            android:value="${CLEVERTAP_TOKEN}" />
        <meta-data
            android:name="FCM_SENDER_ID"
            android:value="${FCM_SENDER_ID}" />
        <meta-data
            android:name="CLEVERTAP_NOTIFICATION_ICON"
            android:value="ic_notif_bar_small" />

        <meta-data
            android:name="SMT_APP_ID"
            android:value="a4d8f6a68beca1a9ae7c727332f5c41e" />
        <meta-data
            android:name="HANSEL_APP_ID"
            android:value="C0QL1T109KU7XKNGWMNFOS96Q" />
        <meta-data
            android:name="HANSEL_APP_KEY"
            android:value="4BJNEUFTW7WJPYKYSTAABCNFK9T574A5REE96NKTZT4J8EH8GD" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_notif_bar_small" />
    </application>

</manifest>