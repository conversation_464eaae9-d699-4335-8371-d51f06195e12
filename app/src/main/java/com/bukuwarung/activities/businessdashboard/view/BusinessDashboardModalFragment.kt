package com.bukuwarung.activities.businessdashboard.view

import android.os.Build
import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.repository.CashRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.FragmentProductDashboardModalBinding
import com.bukuwarung.session.User
import com.bukuwarung.ui.component.bottomsheet.BottomSheetDataHolder
import com.bukuwarung.ui.component.bottomsheet.BottomsheetList
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.setSingleClickListener


class BusinessDashboardModalFragment: BaseFragment() {

    lateinit var binding: FragmentProductDashboardModalBinding
    lateinit var viewModel: BusinessDashboardMainViewModel

    companion object {
        private const val PRODUCT_DASHBOARD_MODAL_FRAGMENT = "product_dashboard_modal_fragment"

        fun createIntent(): BusinessDashboardModalFragment {
            val fragment = BusinessDashboardModalFragment()
            fragment.arguments = Bundle()
            return fragment
        }
    }
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentProductDashboardModalBinding.inflate(layoutInflater, container, false)
        viewModel = activity?.run {
            ViewModelProvider(this).get(BusinessDashboardMainViewModel::class.java)
        }!!
        return binding.root
    }
    override fun setupView(view: View) {
        onMonthChange()
        //TODO Otherwise show blank state
    }

    override fun subscribeState() {
        viewModel.state.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    onMonthChange()
                }
            }
        })
        viewModel.monthState.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    onMonthChange()
                }
            }
        })
    }

    private fun showEmptyState() {
        binding.divider1.visibility = View.INVISIBLE
        binding.btnDetail.visibility = View.INVISIBLE
        binding.emptyInfo.emptyInfoRoot.visibility = View.VISIBLE
        binding.emptyInfo.tvHeader.text = "Apa itu modal?"
        binding.emptyInfo.tvSubtext.text = "Modal adalah uang pribadi, Investor, atau pinjaman bank yang kamu gunakan untuk menjalankan usaha."
        binding.emptyInfo.tvBody.text = "Cara Catat Modal"
        binding.balanceStatus.text = "Catatan modal kamu belum ada. Ayo catat modal kamu sekarang"

        binding.emptyInfo.tvBody.setSingleClickListener {

            startActivity(WebviewActivity.createIntent(requireContext(), "https://bukuwarungsupport.zendesk.com/hc/id-id/articles/*************", "Pusat Bantuan Juragan"))



        }
    }

    private fun getStartDate():  String? {
        val transactionDate = TransactionRepository.getInstance(context).getFirstTransactionDateForCategory(User.getBusinessId(),"Penambahan Modal")
        transactionDate?.let {
            return DateTimeUtils.formatTransactionDate(it)
        }
        return null
    }

    private fun getTransactionAmount(): Double? {
        val amount = TransactionRepository.getInstance(context).getTransactionAmountForCategory(User.getBusinessId(),"Penambahan Modal")
        amount?.let {
            return it
        }
        return 0.0
    }

    fun selectedMonthStartDate():String{
        return (activity as BusinessDashboardMainActivity ).getMonthStartDate()
    }

    fun selectedMonthEndDate():String{
        return (activity as BusinessDashboardMainActivity ).getMonthEndDate()
    }

    private fun onMonthChange() {
        val categoryId = CashRepository.getInstance(context).getCategoryIdByName(User.getBusinessId(),"Penambahan Modal");
        if(categoryId.isNullOrEmpty()) {
            showEmptyState()
            return;
        }
        activateNormalState()
        val startDate = getStartDate()

        startDate?.let {

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                binding.balanceStatus.text = Html.fromHtml(getString(R.string.sejak_kamu, getStartDate(), Utility.formatAmount(getTransactionAmount())), Html.FROM_HTML_MODE_LEGACY);
            } else {
                binding.balanceStatus.text = Html.fromHtml(getString(R.string.sejak_kamu, getStartDate(), Utility.formatAmount(getTransactionAmount())));
            }
            return@let
        }
        binding.btnDetail.setSingleClickListener {
            val modalTransactions = CashRepository.getInstance(
                context
            ).getAllCategoryTransactions(categoryId, User.getBusinessId())
            val dataList = ArrayList<BottomSheetDataHolder>()
            for (transaction in modalTransactions) {
                val item = BottomSheetDataHolder(
                    transaction.cashTransactionId,
                    Utility.formatAmount(transaction.amount),
                    "Penambahan Modal",
                    DateTimeUtils.formatTransactionDate(transaction.date),
                    "https://i.ibb.co/8XxfqR5/penambahan.webp",
                    1,
                    false,
                    true
                )
                dataList.add(item)
            }
            val sheet = BottomsheetList(dataList, dataList, "Riwayat Modal", "",false, true)
            sheet.show(childFragmentManager, "tag")
        }
    }

    private fun activateNormalState() {
        binding.divider1.visibility = View.VISIBLE
        binding.btnDetail.visibility = View.VISIBLE
        binding.emptyInfo.emptyInfoRoot.visibility = View.GONE
    }

}