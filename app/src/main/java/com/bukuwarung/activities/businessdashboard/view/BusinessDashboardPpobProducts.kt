package com.bukuwarung.activities.businessdashboard.view

import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import com.bukuwarung.R
import com.bukuwarung.activities.businessdashboard.model.PersonalPpobProduct
import com.bukuwarung.activities.businessdashboard.model.SalesPpobProduct
import com.bukuwarung.activities.businessdashboard.model.TopProduct
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModelFactory
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentBusinessDashboardPpobBinding
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.ui.component.bottomsheet.BottomSheetDataHolder
import com.bukuwarung.ui.component.bottomsheet.BottomsheetList
import com.bukuwarung.utils.*
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.BarData
import com.github.mikephil.charting.data.BarDataSet
import com.github.mikephil.charting.data.BarEntry
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter
import com.google.android.material.tabs.TabLayout
import javax.inject.Inject

class BusinessDashboardPpobProducts : BaseFragment() {

    lateinit var binding: FragmentBusinessDashboardPpobBinding

    @Inject
    lateinit var vmFactory: BusinessDashboardMainViewModelFactory
    private val viewModel: BusinessDashboardMainViewModel by activityViewModels { vmFactory }
    private var isFirstCard = false

    companion object {
        fun createIntent(isFirstCard: Boolean): BusinessDashboardPpobProducts {
            val fragment = BusinessDashboardPpobProducts()
            fragment.arguments = Bundle().apply {
                putBoolean(BusinessDashboardMainActivity.IS_FIRST_CARD, isFirstCard)
            }
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentBusinessDashboardPpobBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        arguments?.let {
            isFirstCard = it.getBoolean(BusinessDashboardMainActivity.IS_FIRST_CARD)
        }
        val sdate = activity?.let {
            (it as? BusinessDashboardMainActivity)?.getMonthStartDate()
        }
        val edate = activity?.let {
            (it as? BusinessDashboardMainActivity)?.getMonthEndDate()
        }
        viewModel.onEventReceived(
            BusinessDashboardMainViewModel.Event.FetchPpobProducts(
                SessionManager.getInstance(requireContext()).businessId, startDate = sdate ?: "2022-01-01", endDate = edate ?: "2022-01-31", isPersonal = binding.tlTransaction.selectedTabPosition == 1
            )
        )

        binding.tlTransaction.addTab(
            binding.tlTransaction.newTab().setText(context?.getString(R.string.penjualan))
        )
        binding.tlTransaction.addTab(
            binding.tlTransaction.newTab().setText(context?.getString(R.string.pribadi))
        )

        binding.tlTransaction.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                if (tab?.position == 0) {
                    viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.GetPPOBSalesPrefetchedRecord)
                } else {
                    viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.GetPPOBPersonalPrefetchedRecord)
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
            }

        })
        with(binding) {
            if (isFirstCard) {
                ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                clCard.showView()
            } else {
                ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                clCard.hideView()
            }
            clHeading.setOnClickListener {
                if (clCard.visibility == View.VISIBLE) {
                    ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                    clCard.hideView()
                } else {
                    ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                    clCard.showView()
                }
            }
        }
    }

    override fun subscribeState() {
        viewModel.state.observe(this) { eventWrapper ->

            when (val event = eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.SetPpobSalesProducts -> {
                    populateSalesPpobProducts(event.products) // smart-cast
                }
                is BusinessDashboardMainViewModel.State.SetPpobPersonalProducts -> {
                    populatePersonalPpobProducts(event.products)
                }
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    onMonthChange(event.startDate, event.endDate, isPersonal = binding.tlTransaction.selectedTabPosition == 1)
                }
                is BusinessDashboardMainViewModel.State.HidePpobProducts -> {
                    FeaturePrefManager.getInstance().businessDashboardPpobProductShown = true
                    showEmptyState(binding.tlTransaction.selectedTabPosition == 1)
                }
            }
        }

        viewModel.monthState.observe(this) { eventWrapper ->

            when (val event = eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    onMonthChange(event.startDate, event.endDate, isPersonal = binding.tlTransaction.selectedTabPosition == 1)
                }
            }
        }
    }


    private fun populateSalesPpobProducts(products: SalesPpobProduct?) {
        hideEmptyState()

        FeaturePrefManager.getInstance().businessDashboardPpobProductShown = false

        binding.apply {
            cvPenjualanBalance.visibility = View.VISIBLE
            tvTotalPenjualanAmount.text = Utility.formatAmount(products?.totalSales)
            tvTotalModalAmount.text = Utility.formatAmount(products?.totalPurchase)
            val netProfit = products?.totalSales?.minus(products.totalPurchase) ?: 0.0
            tvUntung.text = context?.getString(R.string.untung) + " ${Utility.formatNonAbsoluteAmount(netProfit)}"
            cvPribadiBalance.visibility = View.GONE
            tvBarHeader.text = getString(R.string.ppob_sales_bar_header)
        }

        products?.topProducts?.let {
            initBarChart(it, false)
        }

        binding.btnDetail.setOnClickListener {
            showBottomSheetList(products?.topProducts)
        }
    }

    private fun populatePersonalPpobProducts(products: PersonalPpobProduct?) {
        hideEmptyState()

        FeaturePrefManager.getInstance().businessDashboardPpobProductShown = false

        binding.apply {
            cvPribadiBalance.visibility = View.VISIBLE
            tvTotalPenguluaranAmount.text = Utility.formatAmount(products?.totalPurchase)
            cvPenjualanBalance.visibility = View.GONE
            tvBarHeader.text = getString(R.string.ppob_personal_bar_header)
        }

        products?.topProducts?.let {
            initBarChart(it, true)
        }

        binding.btnDetail.setOnClickListener {

            showBottomSheetList(products?.topProducts)
        }
    }

    private fun showBottomSheetList(products: List<TopProduct>?) {
        val dataList = ArrayList<BottomSheetDataHolder>()
        for (dto in products.orEmpty()) {
            val item = BottomSheetDataHolder(
                id = "",
                left_main_text = dto.productName,
                left_subtext = "",
                right_subtext = dto.count.toString() + " kali terjual",
                icon_image = "",
                index = 1,
                isSelected = false,
                toBeShown = true
            )
            dataList.add(item)
        }

        val sheet = BottomsheetList(
            firstTabList = dataList,
            secondTabList = dataList,
            headerText = "Pulsa dan Tagihan paling laris",
            subheaderText = "Ada 10 daftar produk jualan yang paling sering dibeli oleh pelanggan kamu",
            showTabButton = false,
            showIcon = false
        )
        sheet.show(childFragmentManager, "tag")
    }

    private fun showEmptyState(isPersonal: Boolean) {
        binding.apply {
            ivEmptyStock.showView()
            barChartView.hideView()
            tvTotalPenjualanAmount.text = Utility.formatAmount(0.0)
            tvTotalModalAmount.text = Utility.formatAmount(0.0)
            tvTotalPenguluaranAmount.text = getString(R.string.untung) + " ${Utility.formatAmount(0.0)}"
            tvUntung.text = getString(R.string.untung) + " ${Utility.formatAmount(0.0)}"
            btnDetail.hideView()
            if (isPersonal) {
                cvPenjualanBalance.visibility = View.GONE
                cvPribadiBalance.visibility = View.VISIBLE
                tvBarHeader.text = getString(R.string.ppob_personal_bar_header)
                ivEmptyStock.setImageResource(R.drawable.pribadi_empty)
            } else {
                cvPenjualanBalance.visibility = View.VISIBLE
                cvPribadiBalance.visibility = View.GONE
                tvBarHeader.text = getString(R.string.ppob_sales_bar_header)
                ivEmptyStock.setImageResource(R.drawable.stock_empty)
            }
        }

    }

    private fun hideEmptyState() {
        binding.apply {
            ivEmptyStock.hideView()
            barChartView.showView()
            btnDetail.showView()
        }
    }

    private fun onMonthChange(startDate: String, endDate: String, isPersonal: Boolean) {
        viewModel.onEventReceived(
            BusinessDashboardMainViewModel.Event.FetchPpobProducts(
                SessionManager.getInstance().businessId, startDate, endDate, isPersonal
            )
        )
    }

    private fun initBarChart(topProducts: List<TopProduct>?, isPersonal: Boolean) {
        val xAxis: XAxis = binding.barChartView.xAxis
        xAxis.setDrawGridLines(false)
        xAxis.setDrawAxisLine(false)
        xAxis.granularity = 1f

        binding.barChartView.apply {
            animateY(1000)
            animateX(1000)
            setScaleEnabled(false)
            axisLeft.setDrawGridLines(false)
            axisRight.setDrawGridLines(false)
            axisRight.isEnabled = false
            axisLeft.isEnabled = false
            legend.isEnabled = false
            description.isEnabled = false
            isAutoScaleMinMaxEnabled = true
        }
        val sortedTopProducts = topProducts?.sortedByDescending { it.count }
        showBarChart(sortedTopProducts?.subList(0, minOf(sortedTopProducts.size, 3)), isPersonal)
    }

    private fun showBarChart(products: List<TopProduct>?, isPersonal: Boolean) {
        val entries = ArrayList<BarEntry>()

        if (products != null && products.isEmpty()) {
            FeaturePrefManager.getInstance().businessDashboardPpobProductShown = true

            showEmptyState(isPersonal)
            return
        }

        val sortedProducts = products?.sortedBy { it.count }

        for (i in sortedProducts?.indices!!) {
            val barEntry = BarEntry(i.toFloat(), sortedProducts[i].count.toFloat(), 87)
            entries.add(barEntry)
        }

        val barDataSet = BarDataSet(entries, "label1")
        val colors =
            intArrayOf(
                ContextCompat.getColor(requireContext(), R.color.bar_dashboard_ppob_1),
                ContextCompat.getColor(requireContext(), R.color.bar_dashboard_ppob_2),
                ContextCompat.getColor(requireContext(), R.color.bar_dashboard_ppob_3)
            )

        barDataSet.valueFormatter =
            BusinessDashBoardProductValueFormatter(sortedProducts[sortedProducts.size - 1].count, isPersonal)
        barDataSet.valueTextSize = 12f
        barDataSet.valueTypeface = Typeface.DEFAULT_BOLD
        barDataSet.colors = colors.toMutableList()
        barDataSet.highLightColor = Color.TRANSPARENT
        barDataSet.highLightAlpha = 0
        val xAxis: XAxis = binding.barChartView.xAxis
        xAxis.textSize = 12f
        xAxis.textColor = ContextCompat.getColor(requireContext(), R.color.black_60)
        xAxis.position = XAxis.XAxisPosition.BOTTOM
        xAxis.valueFormatter = IndexAxisValueFormatter(getXAxisValues(sortedProducts))
        val data = BarData(barDataSet)
        data.barWidth = -1f
        binding.barChartView.data = data
        binding.barChartView.setViewPortOffsets(0f, 0f, 0f, 50f)
        val yl: YAxis = binding.barChartView.axisLeft
        yl.axisMinimum = 0f
        yl.mAxisMaximum = sortedProducts[sortedProducts.size - 1].count.toFloat()
        yl.labelCount = 1
        yl.spaceTop = 10f
        binding.barChartView.invalidate()
    }

    private fun getXAxisValues(topProducts: List<TopProduct>): Array<String> {
        var empty = arrayOf<String>()
        for (i in topProducts) {
            var name = i.productName
            if (name.length > 15) {
                name = name.substring(0, 12) + "..."
            }
            empty = append(empty, name)
        }
        return empty
    }
}