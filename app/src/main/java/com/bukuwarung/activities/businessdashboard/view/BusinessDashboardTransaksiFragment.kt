package com.bukuwarung.activities.businessdashboard.view

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Html
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import androidx.viewpager2.widget.ViewPager2
import com.bukuwarung.R
import com.bukuwarung.activities.businessdashboard.adapter.BusinessTransactionBottomSheetViewPager
import com.bukuwarung.activities.businessdashboard.adapter.BusinessTransactionViewPagerAdapter
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.expense.CashListViewModel
import com.bukuwarung.activities.expense.CashListViewModelFactory
import com.bukuwarung.activities.expense.NewCashTransactionActivity
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT2
import com.bukuwarung.constants.AnalyticsConst.SECTION
import com.bukuwarung.constants.AnalyticsConst.TRANSACTION_CARD
import com.bukuwarung.databinding.FragmentProductDashboardTransaksiBinding
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.utils.*
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import javax.inject.Inject

class BusinessDashboardTransaksiFragment: BaseFragment() {
    private lateinit var businessTransactionViewPagerAdapter: BusinessTransactionViewPagerAdapter
    private lateinit var businessBottomSheetViewPager: BusinessTransactionBottomSheetViewPager
    lateinit var binding: FragmentProductDashboardTransaksiBinding
    lateinit var viewModel: BusinessDashboardMainViewModel
    private var isFirstCard = false

    @Inject
    lateinit var cashListViewModelFactory: CashListViewModelFactory
    private lateinit var cashListViewModel: CashListViewModel

    companion object {
        fun createIntent(isFirstCard: Boolean): BusinessDashboardTransaksiFragment {
            val fragment = BusinessDashboardTransaksiFragment()
            fragment.arguments = Bundle().apply {
                putBoolean(BusinessDashboardMainActivity.IS_FIRST_CARD, isFirstCard)
            }
            return fragment
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentProductDashboardTransaksiBinding.inflate(layoutInflater, container, false)
        viewModel = activity?.run {
            ViewModelProvider(this).get(BusinessDashboardMainViewModel::class.java)
        }!!

        cashListViewModel = activity?.run {
            ViewModelProviders.of(this, cashListViewModelFactory).get(
                CashListViewModel::class.java
            )
        }!!

        return binding.root
    }


    override fun setupView(view: View) {
        arguments?.let {
            isFirstCard = it.getBoolean(BusinessDashboardMainActivity.IS_FIRST_CARD)
        }
        businessTransactionViewPagerAdapter = BusinessTransactionViewPagerAdapter(this)
        binding.vpTransaction.adapter = businessTransactionViewPagerAdapter

        binding.vpTransaction.isNestedScrollingEnabled = false

        TabLayoutMediator(binding.tlTransaction, binding.vpTransaction) { tab, position ->
            when (position) {
                0 -> {
                    tab.text = context?.getString(R.string.sales_new)
                }
                else -> {
                    tab.text = context?.getString(R.string.expense_label)
                }
            }
        }.attach()

        binding.btnAddTransaction.setSingleClickListener {

            val propBuilder = AppAnalytics.PropBuilder()
            propBuilder.put(AnalyticsConst.SMS_CHECKBOX_ENABLED, RemoteConfigUtils.shouldSendSmsTransaction())
            propBuilder.put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.shouldShowNewPosInvoice())
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CASH_TAB_ADD_CASH_BTN_CLICKED, propBuilder)

            val intent = NewCashTransactionActivity.createIntent(context)
            intent.putExtra(NewCashTransactionActivity.TRX_TYPE, AppConfigManager.getInstance().transactionType)
            intent.putExtra(NewCashTransactionActivity.SHOW_INTRO, false)
            intent.putExtra(NewCashTransactionActivity.FROM_DAILY_BUSINESS_UPDATE, true)
            startActivity(intent)
        }

        binding.btnDetail.setSingleClickListener {



            val bottomSheetdialog = BottomSheetDialog(requireContext())

            bottomSheetdialog.setContentView(R.layout.bottomsheet_transaksi_business_dashboard)

            bottomSheetdialog.behavior.expandedOffset = 20

            bottomSheetdialog.behavior.state = BottomSheetBehavior.STATE_EXPANDED

            val tabLayout = bottomSheetdialog.findViewById<TabLayout>(R.id.tl_transaction)!!
            val viewPager = bottomSheetdialog.findViewById<ViewPager2>(R.id.vp_transaction)!!

            val btnDismiss = bottomSheetdialog.findViewById<Button>(R.id.btn_dismiss)

            btnDismiss?.setSingleClickListener {
                bottomSheetdialog.dismiss()
            }

            businessBottomSheetViewPager = BusinessTransactionBottomSheetViewPager(this)
            viewPager.adapter = businessBottomSheetViewPager

            viewPager.isNestedScrollingEnabled = false

            TabLayoutMediator(tabLayout, viewPager) { tab, position ->
                when (position) {
                    0 -> {
                        tab.text = context?.getString(R.string.sales_new)
                    }
                    else -> {
                        tab.text = context?.getString(R.string.expense_label)
                    }
                }
            }.attach()


            bottomSheetdialog.show()
        }
        with(binding) {
            if (isFirstCard) {
                ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                clCard.showView()
            } else {
                ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                clCard.hideView()
            }
            clHeading.setOnClickListener {
                if (clCard.visibility == View.VISIBLE) {
                    ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                    clCard.hideView()
                } else {
                    ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                    clCard.showView()
                }
            }
        }
    }


    override fun subscribeState() {

        viewModel.state_transaksi.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.ShowNoOfTransactions -> updateTransactionCount(
                    (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.ShowNoOfTransactions).transactionCount)
            }
        })
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> onMonthChange(it.startDate,it.endDate)
            }
        }
        subscribeSingleLiveEvent(viewModel.monthState) {
            when (it) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> onMonthChange(it.startDate,it.endDate)
            }
        }
    }

    private fun updateTransactionCount(transactionCount: Int) {
        if(transactionCount == 0){
            showEmptyState()
            return
        }
        activateNormalState()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            binding.balanceStatus.text = Html.fromHtml(getString(R.string._2_478_transaksi, transactionCount), Html.FROM_HTML_MODE_LEGACY)
        } else {
            binding.balanceStatus.text = Html.fromHtml(getString(R.string._2_478_transaksi, transactionCount))
        }

    }

    private fun showEmptyState() {
        binding.divider1.visibility = View.GONE
        binding.btnDetail.visibility = View.GONE
        binding.balanceStatus.text = "Kamu belum punya catatan transaksi"
        binding.heading2.text = "BukuWarung bantu kamu catat segala transaksi keuangan usaha jadi lebih efisien dan praktis."
        binding.btnAddTransaction.visibility = View.VISIBLE
        binding.llViewPager.visibility = View.GONE
    }

    private fun selectedMonthStartDate():String{
        return (activity as BusinessDashboardMainActivity ).getMonthStartDate()
    }

    private fun selectedMonthEndDate():String{
        return (activity as BusinessDashboardMainActivity ).getMonthEndDate()
    }

    private fun onMonthChange(sdate:String,edate:String){
        //handle month change listener
        Log.d("TransaksiFragment",sdate+"month changed"+edate)
    }

    private fun activateNormalState() {
        binding.divider1.visibility = View.VISIBLE
        binding.btnDetail.visibility = View.VISIBLE
        binding.btnAddTransaction.visibility = View.GONE
        binding.llViewPager.visibility = View.VISIBLE
    }

}