package com.bukuwarung.activities.businessdashboard.view

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.businessdashboard.view.BusinessDashboardMainActivity.Companion.IS_FIRST_CARD
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModelFactory
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.activities.transactionreport.DateFilter.Companion.getFilterString
import com.bukuwarung.activities.transactionreport.TransactionReportActivity
import com.bukuwarung.activities.transactionreport.TransactionReportActivity.EXTRA_END_DATE
import com.bukuwarung.activities.transactionreport.TransactionReportActivity.EXTRA_START_DATE
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.DEFAULT_FILTER
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.FragmentProductDashboardUtangBinding
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import kotlinx.android.synthetic.main.report_start_end_date.*
import java.text.SimpleDateFormat
import javax.inject.Inject

class BusinessDashboardUtangFragment: BaseFragment() {
    lateinit var binding: FragmentProductDashboardUtangBinding

    @Inject
    lateinit var vmFactory: BusinessDashboardMainViewModelFactory
    private val viewModel: BusinessDashboardMainViewModel by activityViewModels { vmFactory }
    private var isFirstCard = false

    companion object {
        private const val PRODUCT_DASHBOARD_UTANG_FRAGMENT = "product_dashboard_utang_fragment"

        fun createIntent(isFirstCard: Boolean): BusinessDashboardUtangFragment {
            val fragment = BusinessDashboardUtangFragment()
            fragment.arguments = Bundle().apply {
                putBoolean(IS_FIRST_CARD, isFirstCard)
            }
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentProductDashboardUtangBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        arguments?.let {
            isFirstCard = it.getBoolean(BusinessDashboardMainActivity.IS_FIRST_CARD)
        }
        onMonthChange()
        binding.btnDetail.setSingleClickListener{
            val intent = Intent(activity, TransactionReportActivity::class.java)
            intent.putExtra("targetId", "0")
            val formatter1 = SimpleDateFormat("yyyy-MM-dd")
            val start = formatter1.parse(selectedMonthStartDate())
            val end = formatter1.parse(selectedMonthEndDate())

            val formatter2 = SimpleDateFormat("dd-MM-yyyy")
            intent.putExtra(EXTRA_START_DATE, formatter2.format(start))
            intent.putExtra(EXTRA_END_DATE, formatter2.format(end))
            startActivity(intent)
            val prop = PropBuilder()
            prop.put("source", "utang")
            prop.put(DEFAULT_FILTER, getFilterString(requireContext(), 5))
            prop.put(EXTRA_START_DATE, selectedMonthStartDate())
            prop.put(EXTRA_END_DATE, selectedMonthEndDate())
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_TRANSACTION_REPORT, prop)
        }
        with(binding) {
            if (isFirstCard) {
                ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                clCard.showView()
            } else {
                ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                clCard.hideView()
            }
            clHeading.setOnClickListener {
                if (clCard.visibility == View.VISIBLE) {
                    ivDropdown.setImageResource(R.drawable.ic_chevron_down)
                    clCard.hideView()
                } else {
                    ivDropdown.setImageResource(R.drawable.ic_chevron_up)
                    clCard.showView()
                }
            }
        }
    }

    override fun subscribeState() {
        viewModel.monthState.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    onMonthChange()
                }
            }
        })
    }

    private fun showEmptyState() {
        binding.divider1.visibility = View.GONE
        binding.btnDetail.visibility = View.GONE
        binding.emptyInfo.emptyInfoRoot.visibility = View.GONE
        binding.creditBalance.text = "Rp0"
        binding.debitBalance.text = "Rp0"
        binding.emptyInfo.tvBody.setOnClickListener {
            startActivity(WebviewActivity.createIntent(requireContext(), "https://bukuwarungsupport.zendesk.com/hc/id-id/categories/*************", "Pusat Bantuan Juragan"))

        }
    }

    private fun selectedMonthStartDate():String{
        return (activity as BusinessDashboardMainActivity ).getMonthStartDate()
    }

    private fun selectedMonthEndDate():String{
        return (activity as BusinessDashboardMainActivity ).getMonthEndDate()
    }

    private fun onMonthChange(){
        val creditAmount:Double = BusinessRepository.getInstance(context).getCreditByDateRange(User.getBusinessId(),selectedMonthStartDate(),selectedMonthEndDate())
        val debitAmount:Double = BusinessRepository.getInstance(context).getDebitByDateRange(User.getBusinessId(),selectedMonthStartDate(),selectedMonthEndDate())
        if(creditAmount.equals(0.0) && debitAmount.equals(0.0)){
            showEmptyState()
            return
        }
        activateNormalState()
        val balanceAmount = Math.abs(Math.abs(creditAmount) - Math.abs(debitAmount))
        binding.debitBalance.text = Utility.formatAmount(creditAmount)
        binding.creditBalance.text = Utility.formatAmount(debitAmount)

    }

    private fun activateNormalState() {
        binding.divider1.visibility = View.VISIBLE
        binding.btnDetail.visibility = View.VISIBLE
        binding.emptyInfo.emptyInfoRoot.visibility = View.GONE
    }

}