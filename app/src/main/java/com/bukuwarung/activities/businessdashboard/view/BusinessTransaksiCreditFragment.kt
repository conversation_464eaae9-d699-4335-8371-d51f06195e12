package com.bukuwarung.activities.businessdashboard.view

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.activities.businessdashboard.adapter.BusinessTransaksiAdapter
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.expense.CashListViewModel
import com.bukuwarung.activities.expense.CashListViewModelFactory
import com.bukuwarung.activities.expense.adapter.model.DailySummary
import com.bukuwarung.activities.expense.data.Category
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.activities.transaction.category.CategoryTransactionsActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.databinding.FragmentTransaksiBusinessDashboardBinding
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.isNotNullOrEmpty
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import dagger.android.support.AndroidSupportInjection
import java.lang.Exception
import javax.inject.Inject

class BusinessTransaksiCreditFragment: BaseFragment(), BusinessTransaksiAdapter.BusinessTransaksiListener {

    private lateinit var binding: FragmentTransaksiBusinessDashboardBinding
    lateinit var viewModel: BusinessDashboardMainViewModel
    private lateinit var cashAdapter: BusinessTransaksiAdapter

    @Inject
    lateinit var cashListViewModelFactory: CashListViewModelFactory
    private lateinit var cashListViewModel: CashListViewModel

    private var cat:ArrayList<String>? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentTransaksiBusinessDashboardBinding.inflate(inflater, container, false)
        AndroidSupportInjection.inject(this)
        viewModel = activity?.run {
            ViewModelProvider(this).get(BusinessDashboardMainViewModel::class.java)
        }!!
        cashListViewModel = activity?.run {
            ViewModelProviders.of(this, cashListViewModelFactory).get(
                CashListViewModel::class.java
            )
        }!!
        return binding.root
    }

    override fun setupView(view: View) {
        viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.FetchTransaksiRecords(
            cashListViewModel, (activity as BusinessDashboardMainActivity ).getMonthStartDate(),
            (activity as BusinessDashboardMainActivity ).getMonthEndDate()))

        cashAdapter = BusinessTransaksiAdapter(this)
        cashAdapter.setHasStableIds(true)
        binding.productTransaksiUnitRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = cashAdapter
        }
    }

    override fun subscribeState() {
        viewModel.state_bottomsheet_credit.observe(this, androidx.lifecycle.Observer {
            when(it.peekContent()) {
                is BusinessDashboardMainViewModel.State.PopulateBottomSheetPemasukanTransaksiRecords -> updateTransaksiAdapter(
                    (it.peekContent() as BusinessDashboardMainViewModel.State.PopulateBottomSheetPemasukanTransaksiRecords).transaksiRecords)
            }
        })
    }

    fun updateTransaksiAdapter(records: List<DataHolder>) {
        // Data of transaksi here
        Log.d("transaksi data", records.toString())
        val categories = RemoteConfigUtils.SelectCategory.getCreditCategoriesNew()
        val gson: Gson = GsonBuilder().create()
        val jsonType = object : TypeToken<List<Category?>?>() {}.type
        val categoriesToDisplay: MutableList<Category> =
            gson.fromJson(categories, jsonType)
        categoriesToDisplay.add(0,
            Category("https://i.ibb.co/GJ32gQT/penjualan.webp","Penjualan",0,0,"Penjualan"))

        val filteredCategoryList: ArrayList<DataHolder> = arrayListOf()

        cat = ArrayList<String>()

        for (category in categoriesToDisplay) {
            cat!!.add(category.categoryName)
        }

        for (data in records) {
            if (data.name.equals("kasir", true)) {
                filteredCategoryList.add(data)
            }
            if (cat!!.contains(data.name) || data.name!!.contentEquals("Pemasukan")) {
                filteredCategoryList.add(data)
                cat!!.remove(data.name)
            }
        }

        for (category in cat!!) {
                filteredCategoryList.add(
                    DataHolder.CategoryRowHolder(
                        DailySummary(
                            category,
                            "",
                            "",
                            "",
                            0
                        )
                    )
                )
        }

        cashAdapter.refreshView(filteredCategoryList, categoriesToDisplay)
    }

    override fun goToDetail(
        id: String,
        isExpense: Boolean,
        status: Int,
        isDetailTransaksi: Boolean,
        isAutoRecordTxn: Boolean
    ) {
        if (context != null && id.isNotNullOrEmpty()) {
            val intent = CashTransactionDetailActivity.getNewIntent(requireContext(), id, false)
            intent.putExtra(CashTransactionDetailActivity.IS_EXPENSE_PARAM, isExpense)
            intent.putExtra(CashTransactionDetailActivity.TRX_STATUS_PARAM, status)
            intent.putExtra(CashTransactionDetailActivity.IS_DETAIL_TRANSACTION, isDetailTransaksi)
            intent.putExtra(
                CashTransactionDetailActivity.IS_AUTO_RECORD_TRANSACTION,
                isAutoRecordTxn
            )
            startActivity(intent)
        }
    }

    override fun goToCategory(id: String, name: String) {
        if (id.isNotNullOrEmpty()) {
            try {
                val intent = Intent(context, CategoryTransactionsActivity::class.java)
                intent.putExtra(CategoryTransactionsActivity.CASH_CATEGORY_ID, id)
                intent.putExtra(CategoryTransactionsActivity.START_DATE, (activity as BusinessDashboardMainActivity ).getMonthStartDate())
                intent.putExtra(CategoryTransactionsActivity.END_DATE, (activity as BusinessDashboardMainActivity ).getMonthEndDate())
                AppAnalytics.trackEvent("open_cash_transaction_list", "", "")
                startActivity(intent)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
        }
    }

    companion object {
        fun getInstance() = BusinessTransaksiCreditFragment()
    }
}