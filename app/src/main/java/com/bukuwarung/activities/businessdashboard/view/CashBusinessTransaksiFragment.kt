package com.bukuwarung.activities.businessdashboard.view

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.activities.businessdashboard.adapter.BusinessTransaksiAdapter
import com.bukuwarung.activities.businessdashboard.viewmodel.BusinessDashboardMainViewModel
import com.bukuwarung.activities.expense.CashListViewModel
import com.bukuwarung.activities.expense.CashListViewModelFactory
import com.bukuwarung.activities.expense.adapter.model.DailySummary
import com.bukuwarung.activities.expense.data.Category
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.activities.transaction.category.CategoryTransactionsActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.databinding.FragmentCashProductTransaksiBinding
import com.bukuwarung.utils.DateTimeUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bukuwarung.utils.subscribeSingleLiveEvent
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import dagger.android.support.AndroidSupportInjection
import javax.inject.Inject

class CashBusinessTransaksiFragment : BaseFragment(), BusinessTransaksiAdapter.BusinessTransaksiListener {
    private lateinit var binding: FragmentCashProductTransaksiBinding

    lateinit var viewModel: BusinessDashboardMainViewModel
    private lateinit var cashAdapter: BusinessTransaksiAdapter

    @Inject
    lateinit var cashListViewModelFactory: CashListViewModelFactory
    private lateinit var cashListViewModel: CashListViewModel
    private var sdate:String = "2022-01-01";
    private var edate:String = "2022-01-31"

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentCashProductTransaksiBinding.inflate(inflater, container, false)
        AndroidSupportInjection.inject(this)
        viewModel = activity?.run {
            ViewModelProvider(this).get(BusinessDashboardMainViewModel::class.java)
        }!!
        cashListViewModel = activity?.run {
            ViewModelProviders.of(this, cashListViewModelFactory).get(
                CashListViewModel::class.java
            )
        }!!
        return binding.root
    }

    override fun setupView(view: View) {
        sdate = DateTimeUtils.getCurrentMonthStartDate()
        edate = DateTimeUtils.getCurrentMonthEndDate()
        viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.FetchTransaksiRecords(cashListViewModel, sdate,edate))

        cashAdapter = BusinessTransaksiAdapter(this)
        cashAdapter.setHasStableIds(true)
        binding.productTransaksiUnitRecyclerView.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = cashAdapter
        }
    }

    override fun subscribeState() {
        viewModel.state.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.PopulateTransaksiRecords -> updateTransaksiAdapter(
                    (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.PopulateTransaksiRecords).transksiRecords)
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    val data = (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.OnMonthChange)
                    onMonthChange(data.startDate,data.endDate)
                }
            }
        })

        viewModel.monthState.observe(this, Observer { eventWrapper ->
            when (eventWrapper.peekContent()) {
                is BusinessDashboardMainViewModel.State.OnMonthChange -> {
                    val data = (eventWrapper.peekContent() as BusinessDashboardMainViewModel.State.OnMonthChange)
                    onMonthChange(data.startDate,data.endDate)
                }
            }
        })
        subscribeSingleLiveEvent(viewModel.state) { it ->
            when (it) {
                is BusinessDashboardMainViewModel.State.PopulateTransaksiRecords -> updateTransaksiAdapter(it.transksiRecords)
            }
        }
    }

    fun onMonthChange(startDate:String,endDate:String) {
        sdate = startDate
        edate = endDate
        viewModel.onEventReceived(BusinessDashboardMainViewModel.Event.FetchTransaksiRecords(cashListViewModel, sdate,edate))
    }

    fun updateTransaksiAdapter(records: List<DataHolder>) {
        // Data of transaksi here
       val categories = RemoteConfigUtils.SelectCategory.getCreditCategoriesNew()
        val gson: Gson = GsonBuilder().create()
        val jsonType = object : TypeToken<List<com.bukuwarung.activities.expense.data.Category?>?>() {}.type
        val categoriesToDisplay: MutableList<com.bukuwarung.activities.expense.data.Category> = gson.fromJson(categories, jsonType)
        categoriesToDisplay.add(0,
            Category("https://i.ibb.co/GJ32gQT/penjualan.webp","Penjualan",0,0,"Penjualan")
        )

        val filteredCategoryList: ArrayList<DataHolder> = arrayListOf()

        val cat = ArrayList<String>()


        for (category in categoriesToDisplay) {
            cat.add(category.categoryName)
        }

        for (data in records) {
            if (data.name.equals("kasir", true)) {
                filteredCategoryList.add(data)
            }
            if (cat.contains(data.name) || data.name!!.contentEquals("Pemasukan")) {
                filteredCategoryList.add(data)
                cat.remove(data.name)
            }
        }

        for (category in cat) {
            if (filteredCategoryList.size > 2) {
                break
            } else {
                filteredCategoryList.add(
                    DataHolder.CategoryRowHolder(
                        DailySummary(
                            category,
                            "",
                            "",
                            "",
                            0
                        )
                    )
                )
            }
        }

        cashAdapter.refreshView(filteredCategoryList, categoriesToDisplay)
    }

    companion object {
        fun getInstance() = CashBusinessTransaksiFragment()
    }

    override fun goToDetail(
        id: String,
        isExpense: Boolean,
        status: Int,
        isDetailTransaksi: Boolean,
        isAutoRecordTxn: Boolean
    ) {
        if (context != null && id.isNotNullOrEmpty()) {
            val intent = CashTransactionDetailActivity.getNewIntent(requireContext(), id, false)
            intent.putExtra(CashTransactionDetailActivity.IS_EXPENSE_PARAM, isExpense)
            intent.putExtra(CashTransactionDetailActivity.TRX_STATUS_PARAM, status)
            intent.putExtra(CashTransactionDetailActivity.IS_DETAIL_TRANSACTION, isDetailTransaksi)
            intent.putExtra(
                CashTransactionDetailActivity.IS_AUTO_RECORD_TRANSACTION,
                isAutoRecordTxn
            )
            startActivity(intent)
        }
    }

    override fun goToCategory(id: String, name: String) {
        if (id.isNotNullOrEmpty()) {
            try {
                val intent = Intent(context, CategoryTransactionsActivity::class.java)
                intent.putExtra(CategoryTransactionsActivity.CASH_CATEGORY_ID, id)
                intent.putExtra(CategoryTransactionsActivity.START_DATE, (activity as BusinessDashboardMainActivity ).getMonthStartDate())
                intent.putExtra(CategoryTransactionsActivity.END_DATE, (activity as BusinessDashboardMainActivity ).getMonthEndDate())
                AppAnalytics.trackEvent("BD_category_click")
                startActivity(intent)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}