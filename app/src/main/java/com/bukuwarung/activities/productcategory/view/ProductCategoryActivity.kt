package com.bukuwarung.activities.productcategory.view

import android.app.Activity
import android.content.Intent
import android.text.SpannableStringBuilder
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.R
import com.bukuwarung.activities.productcategory.adapter.ProductCategoryAdapter
import com.bukuwarung.activities.productcategory.viewmodel.ProductCategoryViewModel
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.moe.trackEvent
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.entity.ProductCategoryEntity
import com.bukuwarung.databinding.ActivityProductCategoryBinding
import com.bukuwarung.di.ViewModelFactory
import com.bukuwarung.dialogs.GenericConfirmationDialog
import com.bukuwarung.utils.*
import com.google.android.material.snackbar.Snackbar
import javax.inject.Inject

class ProductCategoryActivity : BaseActivity() {
    private val binding: ActivityProductCategoryBinding by lazy {
        ActivityProductCategoryBinding.inflate(layoutInflater).also {
            setContentView(it.root)
        }
    }

    private var categoryCreationDialog : NewProductCategoryDialog? = null

    @Inject
    lateinit var vmFactory: ViewModelFactory<ProductCategoryViewModel>
    private lateinit var viewModel: ProductCategoryViewModel

    private val entryPoint by lazy{
        intent.getStringExtra(ENTRY_POINT)
    }

    private val categoryAdapter: ProductCategoryAdapter by lazy {
        ProductCategoryAdapter { action ->
            when (action) {
                is ProductCategoryAdapter.Action.Check -> {
                    viewModel.onEventReceipt(
                        ProductCategoryViewModel.Event.UpdateSelection(
                            action.categoryId,
                            action.checked
                        )
                    )
                }
                is ProductCategoryAdapter.Action.Delete -> {
                    showDeleteDialog(action.categoryId)
                }
                is ProductCategoryAdapter.Action.Update -> {
                    showCategoryCreationDialog(true, action.categoryId, action.previousCategoryName)
                }
            }
        }
    }


    private fun showCategoryCreationDialog(
        isEditing: Boolean = false,
        categoryId: String = "",
        previousCategoryName: String = ""
    ) {
        categoryCreationDialog =  NewProductCategoryDialog(this, previousName =  previousCategoryName, entryPoint = entryPoint ?: "") { categoryName ->
            if (isEditing) {
                viewModel.onEventReceipt(ProductCategoryViewModel.Event.UpdateCategory(categoryId, categoryName))
            } else {
                viewModel.onEventReceipt(ProductCategoryViewModel.Event.CreateNewCategory(categoryName))
            }
        }.also {
            it.setQueryChangeCallback(lifecycleScope) { query ->
                viewModel.onEventReceipt(ProductCategoryViewModel.Event.RequestCategorySuggestion(query))
            }
        }

        categoryCreationDialog?.show()
    }

    override fun setViewBinding() { /*DO NOTHING*/
    }

    override fun setupView() {
        viewModel = ViewModelProvider(this, vmFactory).get(ProductCategoryViewModel::class.java)

        binding.apply {
            setUpToolbarWithHomeUp(tb)
            btnAddCategory.setOnClickListener {
                trackCreateNewCategoryEvent()
                showCategoryCreationDialog(false, "", etSearch.text.toString())
            }
            btnAddCategoryFromBlank.setOnClickListener {
                trackCreateNewCategoryEvent()
                showCategoryCreationDialog()
            }

            btnSelectCategory.setOnClickListener {
                returnSelectedCategories()
            }
            etSearch.setupForSearch(lifecycleScope, 500){
                viewModel.onEventReceipt(ProductCategoryViewModel.Event.FilterCategory(it))
            }

            val formattedText = SpannableStringBuilder(tvBlankCategoryMessage.text).boldText(getString(R.string.add_new_category_guide_text_to_bold))
            tvBlankCategoryMessage.text = formattedText

            rvProductCategories.apply {
                adapter = categoryAdapter
                layoutManager = LinearLayoutManager(this@ProductCategoryActivity)
                itemAnimator = DefaultItemAnimator()
                addItemDecoration(DividerItemDecoration(this@ProductCategoryActivity, RecyclerView.VERTICAL))
            }
        }

        val existingCategoryIds = intent.getStringArrayExtra(EXISTING_CATEGORY_IDS) ?: emptyArray()
        viewModel.onEventReceipt(ProductCategoryViewModel.Event.UpdateSelections(existingCategoryIds.toList()))
    }

    override fun subscribeState() {
        viewModel.categories.observe(this) { categories ->
            categoryAdapter.submitCategories(categories)
            binding.noRecordFoundTxt.visibility = categories.isEmpty().asVisibility()
        }

        viewModel.categoryCountObservable.observe(this){count ->
            binding.llBlank.visibility = (count == 0).asVisibility()
        }

        subscribeSingleLiveEvent(viewModel.state) { state ->
            when (state) {
                ProductCategoryViewModel.State.OnSelectionCached -> {
                    val selectedIds = viewModel.selectedCategoryList.map { it.id }
                    categoryAdapter.setCheckedItems(selectedIds)
                }
                is ProductCategoryViewModel.State.SetCategorySuggestion -> categoryCreationDialog?.submitSuggestion(state.suggestions)
                ProductCategoryViewModel.State.OnCategoryNameAlreadyExist -> {
                   Snackbar.make(
                       binding.root,
                        getString(R.string.category_name_already_exists),
                        Snackbar.LENGTH_SHORT
                    ).apply {
                       val textColor = getColorCompat(R.color.red_60)
                       setTextColor(textColor)
                       setBackgroundTint(getColorCompat(R.color.red_5))
                       animationMode = Snackbar.ANIMATION_MODE_FADE
                   }.show()
                }
            }
        }
    }

    private fun showDeleteDialog(categoryId: String) {
        GenericConfirmationDialog.create(this){
            titleRes { R.string.delete_categori_confirmation_title }
            bodyRes { R.string.delete_categori_confirmation_body }
            btnRightRes { R.string.label_cancel }
            btnLeftRes { R.string.delete }

            leftBtnCallback = {
                trackEvent(AnalyticsConst.DELETE_PRODUCT_CATEGORY){
                    addProperty(AnalyticsConst.ENTRY_POINT2 to entryPoint)
                }
                viewModel.onEventReceipt(ProductCategoryViewModel.Event.DeleteCategory(categoryId))
            }
        }.show()
    }

    private fun returnSelectedCategories() {
        val selectedCategories = viewModel.selectedCategoryList
        trackCategorySelectionConfirm(selectedCategories)

        val intent = Intent().apply {
            putExtra(SELECTED_CATEGORY_IDS, selectedCategories.map { it.id }.toTypedArray())
        }

        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    private fun trackCreateNewCategoryEvent(){
        trackEvent(AnalyticsConst.CREATE_NEW_PRODUCT_CATEGORY){
            addProperty(AnalyticsConst.ENTRY_POINT2 to entryPoint)
        }
    }

    private fun trackCategorySelectionConfirm(selectedCategories: List<ProductCategoryEntity>) {
        trackEvent(AnalyticsConst.SELECT_PRODUCT_CATEGORY_CLICK) {
            val selectedCategoryNames = selectedCategories.joinToString { it.name }

            addProperty(AnalyticsConst.PRODUCT_CATEGORY_NAME to selectedCategoryNames)
            addProperty(AnalyticsConst.ENTRY_POINT2 to entryPoint)
        }
    }


    companion object {
        const val ENTRY_POINT = "entry_point"
        const val SELECTED_CATEGORY_IDS = "selected_category_ids"
        const val EXISTING_CATEGORY_IDS = "existing_category_ids"

        private fun trackOpenEvent(){
            trackEvent(AnalyticsConst.OPEN_PRODUCT_CATEGORY_SECTION){
                addProperty(AnalyticsConst.ENTRY_POINT2 to AnalyticsConst.PRODUCT_FORM)
            }
        }

        fun startWithResult(activity: Activity, code: Int, existingCategoryIds: List<String> = emptyList(), entryPoint : String) {
            trackOpenEvent()

            Intent(activity, ProductCategoryActivity::class.java).also {
                it.putExtra(ENTRY_POINT, entryPoint)
                it.putExtra(EXISTING_CATEGORY_IDS, existingCategoryIds.toTypedArray())
                activity.startActivityForResult(it, code)
            }
        }
    }


}