package com.bukuwarung.commonview.view

import android.app.PendingIntent
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.ViewModelProviders
import androidx.recyclerview.widget.GridLayoutManager
import com.bukuwarung.Application
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.experiments.CustomWebviewActivity
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.activities.homepage.data.FragmentBlock
import com.bukuwarung.activities.homepage.data.FragmentBodyBlock
import com.bukuwarung.activities.homepage.view.HomeTooltipBottomSheet
import com.bukuwarung.activities.homepage.view.NoInternetAvailableDialog
import com.bukuwarung.activities.homepage.view.PpobPulsaBottomSheet
import com.bukuwarung.activities.homepage.viewmodel.HomePageViewModel
import com.bukuwarung.activities.payment.PaymentTabViewModel
import com.bukuwarung.activities.profile.ProfileTabViewModel
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.commonview.adapter.BukuTileViewAdapter
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.HOMEPAGE_SECTION
import com.bukuwarung.constants.AnalyticsConst.SOURCE
import com.bukuwarung.constants.AppConst.DEEPLINK_INTERNAL_URL
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.databinding.FragmentHomeTilesBinding
import com.bukuwarung.managers.local_notification.LocalNotificationData
import com.bukuwarung.managers.local_notification.LocalNotificationIcon
import com.bukuwarung.managers.local_notification.LocalNotificationManager
import com.bukuwarung.managers.local_notification.LocalNotificationStyle
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.ppob.base.listeners.PpobProductsListener
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.session.SessionManager
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.utils.*
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

class BukuTileView : BaseFragment(), Navigator, OnboardingWidget.OnboardingWidgetListener {

    private lateinit var binding: FragmentHomeTilesBinding
    private lateinit var homeViewModel: HomePageViewModel
    private var fragmentData: FragmentBodyBlock? = null
    private var fragmentBlock: FragmentBlock? = null
    private var isPaymentScreen: Boolean = false
    private var openShowMore: Boolean = false

    val scope = MainScope()

    private val versionCode = BuildConfig.VERSION_CODE
    private var hideReminderIcon = true
    private var hideBnpl = true
    private lateinit var finalItemList: List<BodyBlock?>

    @Inject
    lateinit var profileTabViewModel: ProfileTabViewModel

    @Inject
    lateinit var paymentTabViewModel: PaymentTabViewModel

    @Inject
    lateinit var neuro: Neuro

    var bukuTileAdapter: BukuTileViewAdapter? = null
    private var ppobProductsListener: PpobProductsListener? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentHomeTilesBinding.inflate(layoutInflater, container, false)
        return binding.root
    }

    override fun setupView(view: View) {
        homeViewModel = activity?.run {
            ViewModelProviders.of(this).get(HomePageViewModel::class.java)
        } ?: throw Exception("Invalid Activity")

        fragmentData = arguments?.getParcelable(BUKU_TILE_CONTENT)
        fragmentBlock = arguments?.getParcelable(FRAGMENT_BLOCK)
        isPaymentScreen = arguments?.getBoolean(IS_PAYMENT_SCREEN) ?: false
        openShowMore = arguments?.getBoolean(OPEN_SHOW_MORE) ?: false

        binding.tvHeaderTitle.apply {
            if (!isPaymentScreen) {
                text = fragmentData?.title
                showView()
            } else {
                hideView()
            }
        }

        fragmentData?.tooltip_title?.let { title ->
            binding.tvHelpIcon.showView()
            binding.tvHelpIcon.setOnClickListener {
                HomeTooltipBottomSheet.newInstance(title, fragmentData?.tooltip_body)
                    .show(childFragmentManager, "HomeTooltipBottomSheet")
            }
        } ?: run {
            binding.tvHelpIcon.hideView()
        }

        fragmentData?.let {
            binding.rvTileView.apply {
                finalItemList = filterList(it)
                this.isNestedScrollingEnabled = false
                val isListLong = finalItemList.size > getNumberOfColumns()
                bukuTileAdapter = BukuTileViewAdapter(
                    getNumberOfColumns(),
                    isListLong,
                    if (isListLong) finalItemList.take(getNumberOfColumns() - 1) else finalItemList,
                    fragmentBlock?.category!!
                ) { fragmentBody, category, position ->
                    if (isListLong && position == getNumberOfColumns() - 1) {
                       openPpobBottomSheet(category)
                    } else {
                        if (isPaymentScreen) {
                            ppobProductsListener?.onPpobSelected(fragmentBody)
                        } else {
                            run {
                                processRedirectAndAnalytics(fragmentBody, category)
                            }
                        }
                    }
                }
                layoutManager = GridLayoutManager(requireContext(), getNumberOfColumns())
                adapter = bukuTileAdapter

                if (openShowMore) {
                    openPpobBottomSheet(fragmentBlock?.category!!)
                }
            }
        }

        if (fragmentData?.body_block_name.equals("lainnya_body", true)) {
            profileTabViewModel.onEventReceived(ProfileTabViewModel.Event.OnBnplDetailsRequested)
        }

    }

    override fun onResume() {
        super.onResume()

    }

    private fun openPpobBottomSheet(category: String) {
        val bukuTileBottomSheet = BukuTileViewBottomSheet.createInstance(
            fragmentBlock?.category, isPaymentScreen
        )
        bukuTileBottomSheet.setPpobProductsListener(ppobProductsListener)
        bukuTileBottomSheet.show(childFragmentManager, BukuTileViewBottomSheet.TAG)

        val prop = AppAnalytics.PropBuilder()
        prop.put(AnalyticsConst.HOMEPAGE_SECTION, category)
        prop.put(AnalyticsConst.HomePage.BUTTON_NAME, category.plus("_semua"))
        prop.put(AnalyticsConst.HomePage.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
        prop.put(SOURCE, AnalyticsConst.HomePage.HOMEPAGE)
        AppAnalytics.trackEvent(AnalyticsConst.HomePage.EVENT_HOMEPAGE_BUTTON_CLICK, prop)
    }

    private fun filterList(fragmentData: FragmentBodyBlock): List<BodyBlock?> {
        val versionFilteredList = fragmentData.data!!.filter { it?.start_version!! <= versionCode && (it.end_version >= versionCode || it.end_version == -1) }.sortedBy { it?.rank }
        val reminderFilteredList = if (hideReminderIcon) {
            versionFilteredList.filter { !it?.analytics_name!!.contains(REMINDER, true) }
        } else {
            versionFilteredList
        }
        val referralFilteredList = if (RemoteConfigUtils.showReferralEntryPointHome() && fragmentData.body_block_name.equals("lainnya_body")) {
            reminderFilteredList.filter { !it?.analytics_name!!.contains(
                UNDANG_TEMAN, true) }
        } else {
            reminderFilteredList
        }
        val bnplFilterList = if (hideBnpl) {
            referralFilteredList.filter { data -> !data?.analytics_name.equals(TALAHGIN_DULU, true) }
        } else {
            referralFilteredList
        }
        return bnplFilterList
    }

    override fun subscribeState() {

        PaymentPrefManager.getInstance().reminderFlag.observe(viewLifecycleOwner) {
            hideReminderIcon = !it
            fragmentData?.let { data ->
                val fragData = filterList(data)
                refreshContents(fragData)
            }
        }

        subscribeSingleLiveEvent(profileTabViewModel.state) {
            when (it) {
                ProfileTabViewModel.State.ShowBnplEntryPoint -> {
                    hideBnpl = false
                }
                ProfileTabViewModel.State.HideBnplEntryPoint -> {
                    hideBnpl = true
                }
            }

            fragmentData?.let { data ->
                val fragData = filterList(data)
                refreshContents(fragData)
            }
        }

        subscribeSingleLiveEvent(paymentTabViewModel.state) {
            when (it) {
                is PaymentTabViewModel.State.ShowPpobPulsaBottomsheet -> {
                    showPpobPulsaBottomSheet(it.merchantCategory)
                }

                is PaymentTabViewModel.State.ShowPpobListrikBottomsheet -> {
                    showPpobListrikBottomSheet(it.merchantCategory)
                }

            }
        }
    }

    private fun showPpobPulsaBottomSheet(merchantCategory: String?) {
        if (SessionManager.getInstance().hasPpobPulsaSeenCount() < 2 && !SessionManager.getInstance().hasPpobPulsaSeen()) {
            createPN("pulsa", merchantCategory)
            val ppobBottomSheet = PpobPulsaBottomSheet.newInstance("pulsa", merchantCategory)
            ppobBottomSheet.show(activity?.supportFragmentManager!!, "ppob_pulsa")
            SessionManager.getInstance().setPpobPulsaBottomSheetSeenCount()
            val prop = AppAnalytics.PropBuilder()
            prop.put(AnalyticsConst.CROSS_POPUP_COUNT, SessionManager.getInstance().hasPpobPulsaSeenCount())
            AppAnalytics.trackEvent(AnalyticsConst.CROSS_ADOPTION_POPUP_APPEAR, prop)
        }
    }

    private fun showPpobListrikBottomSheet(merchantCategory: String?) {
        if (SessionManager.getInstance().hasPpobListrikSeenCount() < 2 && !SessionManager.getInstance().hasPpobListrikSeen()) {
            createPN("listrik", merchantCategory)
            val ppobBottomSheet = PpobPulsaBottomSheet.newInstance("listrik", merchantCategory)
            ppobBottomSheet.show(activity?.supportFragmentManager!!, "ppob_listrik")
            SessionManager.getInstance().setPpobListrikBottomSheetSeenCount()
            val prop = AppAnalytics.PropBuilder()
            prop.put(AnalyticsConst.CROSS_POPUP_COUNT, SessionManager.getInstance().hasPpobListrikSeenCount())
            AppAnalytics.trackEvent(AnalyticsConst.CROSS_ADOPTION_POPUP_APPEAR, prop)
        }
    }

    private fun createPN(source: String, merchantCategory: String?) {
        val localNotificationData = LocalNotificationData(
            resources.getString(R.string.notification_ppob_bottomsheet),
            resources.getString(R.string.notification_ppob_body),
            LocalNotificationIcon.DEFAULT
        )
        val random = Random()
        val randomNumber = random.nextInt(1000)
        val intent = Intent(activity, MainActivity::class.java)
        intent.putExtra("show_ppob_bottomsheet", true)
        intent.putExtra("show_ppob_bottomsheet_val", source)
        intent.putExtra("merchant_type", merchantCategory)
        val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PendingIntent.getActivities(activity, randomNumber, arrayOf(intent), PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
        } else {
            PendingIntent.getActivities(activity, randomNumber, arrayOf(intent), PendingIntent.FLAG_UPDATE_CURRENT)
        }
        LocalNotificationManager.showDefaultNotification(
            Application.getAppContext(),
            localNotificationData,
            LocalNotificationStyle.BIG_TEXT,
            pendingIntent, LocalNotificationManager.NOTIFICATION_CHANNEL_TITLE
        )
    }

    companion object {
        private const val TAG = "buku-tile-view-bottomsheet"
        private const val PPOB = "ppob"
        private const val BUKU_TILE_CONTENT = "buku_tile_content"
        private const val FRAGMENT_BLOCK = "fragment_block"
        private const val IS_PAYMENT_SCREEN = "is_payment_screen"
        private const val OPEN_SHOW_MORE = "open_show_more"
        const val REMINDER = "reminder"
        const val UNDANG_TEMAN = "undang_teman"
        const val TALAHGIN_DULU = "talagin_dulu"

        fun createIntent(
            bukuTileContent: FragmentBodyBlock, fragmentBlock: FragmentBlock,
            isPaymentScreen: Boolean = false, listener: PpobProductsListener? = null, openShowMore: Boolean = false
        ): BukuTileView {
            val fragment = BukuTileView()
            fragment.ppobProductsListener = listener
            fragment.arguments = Bundle().apply {
                putParcelable(BUKU_TILE_CONTENT, bukuTileContent)
                putParcelable(FRAGMENT_BLOCK, fragmentBlock)
                putBoolean(IS_PAYMENT_SCREEN, isPaymentScreen)
                putBoolean(OPEN_SHOW_MORE, openShowMore)
            }

            return fragment
        }
    }

    private fun getNumberOfColumns(): Int {
        val displayMetrics: DisplayMetrics = requireContext().resources.displayMetrics
        val dpWidth: Float = displayMetrics.widthPixels / displayMetrics.density
        return (dpWidth / 70).toInt()
    }

    private fun processRedirectAndAnalytics(fragmentBody: BodyBlock?, category: String) {
        // Redirection/ deeplink/ flow
        val newRedirection = fragmentBody?.deeplinkAppNeuro.orEmpty()
        val redirection = fragmentBody?.deeplink_app.orEmpty()
        val deeplink = fragmentBody?.deeplink_web.orEmpty()
        when {
            newRedirection.isNotBlank() -> {
                redirect(newRedirection, category)
            }
            newRedirection.isBlank() && redirection.isNotBlank() -> {
                redirect(redirection, category)
            }
            deeplink.isNotBlank() -> {
                if (Utility.hasInternet()) {
                    if (fragmentBody?.analytics_name?.contains("reward")!!) {
                        val webViewIntent = CustomWebviewActivity.createIntent(
                            activity,
                            deeplink, "Integrasi Institusi Keuangan", false,
                            "loyalty_account", "homepage"
                        )
                        startActivity(webViewIntent)
                    } else {
                        startActivity(
                            WebviewActivity.createIntent(
                                requireActivity(), deeplink,
                                fragmentBody.display_name
                            )
                        )
                    }
                } else {
                    NoInternetAvailableDialog.show(childFragmentManager)
                }
            }
        }
        handleAnalytics(fragmentBody, category)
    }

    private fun redirect(redirection: String, category: String) {
        if (Utility.hasInternet()) {
            if (category == PPOB && PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_PPOB)) {
                PaymentUtils.showKycKybStatusBottomSheet(
                    childFragmentManager,
                    AnalyticsConst.HOME_PAGE
                )
                return
            }
            val sourceLink = SourceLink(context = requireContext(), link = redirection)
            neuro.route(
                sourceLink,
                navigator = this,
                onSuccess = {},
                onFailure = { redirectWithLegacyLink(redirection) },
            )
        } else {
            NoInternetAvailableDialog.show(childFragmentManager)
        }
    }

    private fun redirectWithLegacyLink(redirection: String) {
        if (!redirection.startsWith("com.")) return

        val link = "$DEEPLINK_INTERNAL_URL?type=act&data=$redirection&from=home&launch=1"
        val sourceLink = SourceLink(context = requireContext(), link)

        neuro.route(sourceLink, navigator = this, onSuccess = {}, onFailure = {})
    }

    private fun handleAnalytics(fragmentBody: BodyBlock?, category: String) {
        val prop = AppAnalytics.PropBuilder()
        prop.put(HOMEPAGE_SECTION, category)
        prop.put(AnalyticsConst.HomePage.BUTTON_NAME, fragmentBody?.analytics_name)
        prop.put(AnalyticsConst.HomePage.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
        prop.put(AnalyticsConst.HomePage.HOMEPAGE_PPOB_PROMO_TAG_ENABLED, fragmentBody?.is_promo)
        prop.put("entry_point_detail", AnalyticsConst.HomePage.HOMEPAGE)
        AppAnalytics.trackEvent("homepage_button_click", prop)
    }

    private fun refreshContents(bukuTileContent: List<BodyBlock?>) {
        finalItemList = bukuTileContent
        bukuTileAdapter?.refreshData(bukuTileContent)
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }

    private fun showCoachmark(fragmentData: FragmentBlock?) {
        fragmentData?.let {

            if (isAdded && activity != null && !requireActivity().isFinishing) {
                scope.launch {
                    delay(200)
                    OnboardingWidget.createInstance(
                        requireActivity(),
                        this@BukuTileView,
                        fragmentData.analytics_block_name,
                        binding.vwCoachmark,
                        null,
                        fragmentData.coachmark_header!!,
                        fragmentData.coachmark_body!!,
                        getString(R.string.next),
                        FocusGravity.CENTER,
                        ShapeType.RECTANGLE_FULL,
                        fragmentData?.coachmark_step!!,
                        fragmentData.coachmark_max_steps!!,
                        sendAnalytics = true,
                        sendAnalyticsOnDismiss = true,
                        delay = 5,
                        isSnackBarClick = false,
                        isPerformClick = true
                    )
                }
            }
        }
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
        // nO imp needed
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        homeViewModel.onEventReceived(HomePageViewModel.Event.OnNextCoachmarkClick(fragmentBlock?.coachmark_next))
        if (RemoteConfigUtils.isCrossSellPopupEnabled()) {
            paymentTabViewModel.getFirstTransaction()
        }
    }
}