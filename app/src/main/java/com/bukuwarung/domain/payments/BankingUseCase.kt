package com.bukuwarung.domain.payments

import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.payments.data.model.BankingFeatureFlags
import com.bukuwarung.payments.data.model.QrisResponse
import com.bukuwarung.payments.data.repository.BankingRepository
import com.bukuwarung.payments.pref.PaymentPrefManager

class BankingUseCase(private val repository: BankingRepository) {

    suspend fun getQrisStatus(bookId: String): ApiResponse<QrisResponse> =
        repository.getQrisStatus(bookId)

    suspend fun getFeatureFlags(): ApiResponse<BankingFeatureFlags> = repository.getFeatureFlags()

    private suspend fun updateQrisDetails(
        qrisAccountId: String,
        qrisUpdateMap: Map<String, String>
    ) = repository.updateQrisDetails(qrisAccountId, qrisUpdateMap)

    suspend fun getQrisBatchSettlementDashboard(
        userId: String,
        startDate: String,
        endDate: String
    ) = repository.getQrisBatchSettlementDashboard(userId, startDate, endDate)

    suspend fun updateQrisBookName(bookId: String, bookName: String): ApiResponse<Unit>? {
        val qrisInfo = PaymentPrefManager.getInstance().getQrisInfo()
        if (bookId != qrisInfo.qrisBookId) return null
        val qrisAccountId = qrisInfo.virtualAccountId ?: return null
        return updateQrisDetails(qrisAccountId, mapOf("business_name" to bookName))
    }

    suspend fun getCashbackList(
        userId: String,
        pageCount: Int,
        bookId: String? = null,
        start_date: String? = null,
        end_date: String?,
        disbursableType: String?
    ) =
        repository.getCashbackList(userId, pageCount, bookId, start_date, end_date, disbursableType)

}
