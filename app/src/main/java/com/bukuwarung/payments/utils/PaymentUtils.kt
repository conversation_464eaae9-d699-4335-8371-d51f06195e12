package com.bukuwarung.payments.utils

import android.content.Context
import android.content.Intent
import androidx.fragment.app.FragmentManager
import androidx.work.*
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.entity.Bank
import com.bukuwarung.database.entity.RefundBankAccount
import com.bukuwarung.database.entity.UrlType
import com.bukuwarung.payments.bottomsheet.KycKybBottomSheet
import com.bukuwarung.payments.bottomsheet.PaymentLimitsBottomSheet
import com.bukuwarung.payments.constants.*
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_BPJS
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_EWALLET
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_LISTRIK
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_MULTIFINANCE
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_PAKET_DATA
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_PDAM
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_PLN_POSTPAID
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_PULSA
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_PULSA_POSTPAID
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_VEHICLE_TAX
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_VOUCHER_GAME
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.ppob.base.model.PpobProductModel
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.qris.QrisActivity
import com.bukuwarung.payments.widget.SwitchToQrisBookBottomSheet
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*
import com.bukuwarung.utils.DateTimeUtils.DD_MMM_YYYY
import com.bukuwarung.utils.DateTimeUtils.getTimestampFromUtcDate
import com.google.firebase.crashlytics.FirebaseCrashlytics
import org.json.JSONException
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

object PaymentUtils {
    fun getPpobCategoryName(context: Context, category: String?): String {
        if (category.isNullOrBlank()) return ""
        val id = PpobConst.CATEGORY_NAME_MAP[category]
        return if (id != null) context.getString(id) else ""
    }

    fun isPpob(category: String?): Boolean {
        category ?: return false
        var b = false
        for ((key, _) in PpobConst.CATEGORY_NAME_MAP) {
            b = category.toLowerCase().contains(key.toLowerCase())
            if (b) break
        }
        return b
    }

    const val TEXT_COLOR = "text_color"
    const val TEXT_BG_RES = "text_bg_res"
    const val TEXT_MESSAGE = "text_message"
    const val ICON_RES = "icon_res"
    const val ICON_COLOR = "icon_color"
    const val TEXT_STYLE = "text_style"

    /**
     * Returns a map that defines label for the status text view and color of the text.
     * First it checks for type of the payment i.e. ppob/saldo/payment_in etc
     * Then checks for the status of the payment.
     */
    fun getPaymentStatusLabelData(
        type: String?,
        status: String?,
        context: Context
    ): MutableMap<String, Any>? {
        return when (type) {
            PaymentHistory.TYPE_CASHBACK_SETTLEMENT -> {
                val defaultIcon = R.drawable.ic_komisi_agen_dollar
                val failedIcon = R.drawable.ic_komisi_agen_dollar_expired
                getPaymentTrxIconAndStatusWording(context, status, defaultIcon, failedIcon)
            }
            PaymentHistory.TYPE_SALDO_IN, PaymentHistory.TYPE_SALDO_REFUND, PaymentHistory.TYPE_SALDO_OUT -> {
                val defaultIcon =
                    if (type == PaymentHistory.TYPE_SALDO_IN || type == PaymentHistory.TYPE_SALDO_REFUND) R.drawable.ic_saldo_in_new else R.drawable.ic_saldo_out_new
                val failedIcon =
                    if (type == PaymentHistory.TYPE_SALDO_IN || type == PaymentHistory.TYPE_SALDO_REFUND) R.drawable.ic_saldo_in_grey_new else R.drawable.ic_saldo_out_grey_new
                getPaymentTrxIconAndStatusWording(context, status, defaultIcon, failedIcon)
            }
            PaymentHistory.TYPE_PAYMENT_IN, PaymentHistory.TYPE_PAYMENT_OUT -> {
                val defaultIcon =
                    if (type == PaymentHistory.TYPE_PAYMENT_IN) R.drawable.ic_payment_in else R.drawable.ic_payment_out
                val failureIcon =
                    if (type == PaymentHistory.TYPE_PAYMENT_IN) R.drawable.ic_payment_in_failed else R.drawable.ic_payment_out_failed
                getPaymentTrxIconAndStatusWording(context, status, defaultIcon, failureIcon)
            }
            PaymentHistory.TYPE_SALDO_REDEMPTION -> mutableMapOf(
                TEXT_MESSAGE to context.getString(R.string.payment_status_completed),
                ICON_RES to R.drawable.ic_saldo_cashback,
                TEXT_STYLE to R.style.Body3_black40
            )
            PaymentHistory.TYPE_SALDO_CASHBACK -> {
                getPaymentTrxIconAndStatusWording(context, status, R.drawable.ic_saldo_cashback, 0, true)
            }
            PaymentConst.TYPE_CASHBACK_IN, PaymentConst.TYPE_CASHBACK_OUT -> {
                val defaultIcon =
                    if (type == PaymentConst.TYPE_CASHBACK_IN) R.drawable.ic_cashback_in else R.drawable.ic_cashback_out
                val failureIcon =
                    if (type == PaymentConst.TYPE_CASHBACK_IN) R.drawable.ic_cashback_in_greyed else R.drawable.ic_cashback_out_greyed
                getPaymentTrxIconAndStatusWording(context, status, defaultIcon, failureIcon, true)
            }
            PaymentConst.TYPE_PAYMENT_OUT_SUBSCRIPTION -> {
                val defaultIcon = R.drawable.vector_payment_out_subscription
                val failureIcon = R.drawable.vector_payment_out_subscription_grey
                getPaymentTrxIconAndStatusWording(context, status, defaultIcon, failureIcon, true)
            }
            else -> {
                if (isPpob(type))
                    getPaymentTrxIconAndStatusWording(context, status, PpobConst.CATEGORY_ICON_CIRCLE_MAP[type]!!, PpobConst.CATEGORY_EXPIRED_ICON_CIRCLE_MAP[type]!!)
                else
                    getPaymentTrxIconAndStatusWording(context, status, 0, 0)
            }
        }
    }

    private fun getPaymentTrxIconAndStatusWording(
        context: Context,
        status: String?,
        defaultIcon: Int,
        failedIcon: Int,
        isCashBackTrx: Boolean = false
    ): MutableMap<String, Any>? {
        val baseMap: MutableMap<String, Any> = mutableMapOf(
            ICON_RES to defaultIcon,
            TEXT_STYLE to R.style.Body3_black40
        )
        return when (status) {
            PaymentHistory.STATUS_EXPIRED -> {
                baseMap[ICON_RES] = failedIcon
                baseMap[TEXT_MESSAGE] = context.getString(R.string.expired_label)
                baseMap
            }
            PaymentHistory.STATUS_CREATED,
            PaymentHistory.STATUS_PENDING -> {
                baseMap[TEXT_MESSAGE] =
                    context.getString(if (isCashBackTrx) R.string.waiting_label else R.string.in_process)
                baseMap
            }
            PaymentHistory.STATUS_HOLD,
            PaymentHistory.STATUS_PAID -> {
                baseMap[TEXT_MESSAGE] = context.getString(R.string.in_process)
                baseMap
            }
            PaymentHistory.STATUS_COMPLETED -> {
                baseMap[TEXT_MESSAGE] = context.getString(R.string.payment_status_completed)
                baseMap
            }
            PaymentHistory.STATUS_CANCELLED -> {
                baseMap[ICON_RES] = failedIcon
                baseMap[TEXT_MESSAGE] = context.getString(R.string.cancelled_label)
                baseMap
            }
            PaymentHistory.STATUS_REJECTED,
            PaymentHistory.STATUS_FAILED -> {
                baseMap[TEXT_MESSAGE] = context.getString(R.string.fail_label)
                baseMap[TEXT_STYLE] = R.style.Body3_red80Bold
                baseMap
            }
            PaymentHistory.STATUS_REFUNDING -> {
                baseMap[TEXT_MESSAGE] = context.getString(R.string.refund_in_process)
                baseMap
            }
            PaymentHistory.STATUS_REFUNDED -> {
                baseMap[TEXT_MESSAGE] = context.getString(R.string.refund_successful)
                baseMap[TEXT_STYLE] = R.style.Body3_green80
                baseMap
            }
            PaymentHistory.STATUS_REFUNDING_FAILED -> {
                baseMap[TEXT_MESSAGE] = context.getString(R.string.refund_failed_1)
                baseMap[TEXT_STYLE] = R.style.Body3_red80Bold
                baseMap
            }
            else -> null
        }
    }

    /**
     * Returns a map that defines color and icon for any payment progress.
     */
    fun getPaymentProgressData(
        context: Context, paymentProgress: PaymentProgress
    ): MutableMap<String, Any> {
        return when {
            PaymentHistory.STATUS_RED_LIST.contains(paymentProgress.state.toLowerCase()) -> {
                mutableMapOf(
                    TEXT_COLOR to context.getColorCompat(R.color.out_red),
                    ICON_RES to R.drawable.ic_cross_filled_circle,
                    ICON_COLOR to context.getColorCompat(R.color.out_red)
                )
            }
            (paymentProgress.state.equals(
                PaymentHistory.STATUS_COMPLETED,
                ignoreCase = true
            ) && paymentProgress.timestamp.isNullOrBlank() && paymentProgress.hasNextTimestamp)
                    || (paymentProgress.state.equals(
                PaymentHistory.STATUS_PENDING,
                ignoreCase = true
            ) && paymentProgress.timestamp.isNotNullOrBlank() && !paymentProgress.hasPreviousTimestamp)
                    || paymentProgress.state.equals(
                PaymentHistory.STATUS_REFUNDING,
                ignoreCase = true
            ) || paymentProgress.state.equals(
                PaymentHistory.STATUS_HOLD,
                ignoreCase = true
            ) -> {
                mutableMapOf(
                    TEXT_COLOR to context.getColorCompat(R.color.yellow),
                    ICON_RES to R.drawable.ic_processing_filled_circle,
                    ICON_COLOR to context.getColorCompat(R.color.transparent)
                )
            }
            paymentProgress.state.equals(PaymentHistory.STATUS_UNHOLD, ignoreCase = true) -> {
                mutableMapOf(
                    TEXT_COLOR to context.getColorCompat(R.color.green),
                    ICON_RES to R.drawable.ic_check_circle_60,
                    ICON_COLOR to context.getColorCompat(R.color.green)
                )
            }
            paymentProgress.timestamp.isNullOrEmpty().not() -> {
                mutableMapOf(
                    TEXT_COLOR to context.getColorCompat(R.color.green),
                    ICON_RES to R.drawable.ic_check_circle_60,
                    ICON_COLOR to context.getColorCompat(R.color.green)
                )
            }
            else -> mutableMapOf(
                TEXT_COLOR to context.getColorCompat(R.color.black_40),
                ICON_RES to R.drawable.ic_check_circle_60,
                ICON_COLOR to context.getColorCompat(R.color.black_40)
            )
        }
    }

    /**
     * Returns a map that defines style for the amount text.
     */
    fun getPaymentAmountData(
        type: String?,
        status: String?
    ): MutableMap<String, Any>? {
        val baseMap: MutableMap<String, Any> = mutableMapOf(
            TEXT_STYLE to R.style.SubHeading1_black40
        )
        return when (type) {
            PaymentHistory.TYPE_SALDO_IN,
            PaymentHistory.TYPE_SALDO_REFUND,
            PaymentHistory.TYPE_PAYMENT_IN,
            PaymentHistory.TYPE_SALDO_REDEMPTION,
            PaymentConst.TYPE_CASHBACK_IN,
            PaymentHistory.TYPE_SALDO_CASHBACK -> {
                when (status) {
                    PaymentHistory.STATUS_PENDING,
                    PaymentHistory.STATUS_COMPLETED,
                    PaymentHistory.STATUS_PAID -> {
                        baseMap[TEXT_STYLE] = R.style.SubHeading1_green80
                        baseMap
                    }
                    else -> baseMap
                }
            }
            PaymentConst.TYPE_CASHBACK_OUT,
            PaymentHistory.TYPE_SALDO_OUT,
            PaymentHistory.TYPE_PAYMENT_OUT -> {
                when (status) {
                    PaymentHistory.STATUS_PENDING,
                    PaymentHistory.STATUS_COMPLETED,
                    PaymentHistory.STATUS_PAID -> {
                        baseMap[TEXT_STYLE] = R.style.SubHeading1_red80
                        baseMap
                    }
                    else -> baseMap
                }
            }
            else -> {
                if (isPpob(type)) {
                    when (status) {
                        PaymentHistory.STATUS_PENDING,
                        PaymentHistory.STATUS_COMPLETED,
                        PaymentHistory.STATUS_PAID -> {
                            baseMap[TEXT_STYLE] = R.style.SubHeading1_red80
                            baseMap
                        }
                        else -> baseMap
                    }
                } else {
                    baseMap[TEXT_STYLE] = R.style.SubHeading1_black80
                    baseMap
                }
            }
        }
    }

    /**
     * Returns reason for transaction to be in pending/expired/failed/cancelled state.
     * Return empty reason for other states.
     */
    fun getReasonForUnsuccessfulPayment(order: FinproOrderResponse): String {
        val progress = when (order.status) {
            PaymentHistory.STATUS_PENDING -> {
                order.progress?.firstOrNull { it.state == PaymentHistory.STATUS_PENDING }
            }
            PaymentHistory.STATUS_EXPIRED -> {
                order.progress?.firstOrNull { it.state == PaymentHistory.STATUS_EXPIRED }
            }
            PaymentHistory.STATUS_FAILED -> {
                order.progress?.firstOrNull { it.state == PaymentHistory.STATUS_FAILED }
            }
            PaymentHistory.STATUS_CANCELLED -> {
                order.progress?.firstOrNull { it.state == PaymentHistory.STATUS_CANCELLED }
            }
            else -> null
        }
        return progress?.let { getReason(it) } ?: ""
    }

    private fun getReason(progress: PaymentProgress) =
        progress.additional_info ?: progress.description ?: ""

    /**
     * Returns "Total Diterima(Total Received)" amount after deducting admin and other fees like qris
     */
    fun getTotalReceivedAmount(order: FinproOrderResponse, item: PpobProductDetail?): Double? {
        var totalReceived = item?.amount
        // Deduct admin fee
        item?.discountedFee?.let {
            totalReceived = totalReceived?.minus(it)
        }
        order.loyalty?.tierDiscount?.let {
            totalReceived = totalReceived?.plus(it)
        }
        order.loyalty?.subscriptionDiscount?.let {
            totalReceived = totalReceived?.plus(it)
        }

        // Deduct QRIS fee
        val qrisDetail = order.payments?.firstOrNull()?.paymentMethod?.detail
        qrisDetail?.qrisFee?.let {
            if (it > 0.0) {
                totalReceived = totalReceived?.minus(it)
            }
        }

        return totalReceived
    }

    /**
     * Returns date (of device) when the next retry attempt is possible.
     * It depends on the last retry attempt's timestamp.
     * This should handle the difference between device's timestamp and server timestamp
     * of last retry.
     */
    fun getNextRetryAvailableTime(
        currentServerTS: String,
        disbursementTime: String
    ): String {
        val serverTS = getTimestampFromUtcDate(currentServerTS)
        // This is UTC+0 time
        val serverCal = Calendar.getInstance(TimeZone.getTimeZone("UTC"))
        serverCal.timeInMillis = serverTS

        // disbursementTime is in UTC+7 (WIB), ex 22:00 WIB
        val pair = parseHourAndMins(disbursementTime)
        val hour = pair.first

        // Note: This is an estimate time, so ignoring minutes, also we are assuming disbursementTime
        // is UTC+7, hence adding 7 to the HOUR_OF_DAY
        // If hour of serverTS is < disbursementTime hours, user can try today after disbursementTime
        // Else user can only try the next day
        if ((serverCal.get(Calendar.HOUR_OF_DAY) + 7) >= (hour)) {
            serverCal.add(Calendar.DATE, 1)
        }
        return DateTimeUtils.getFormattedDateTime(serverCal.time.time, DD_MMM_YYYY)
    }

    /**
     * Returns value of QRIS-[app] that was used to make QRIS payment
     */
    fun getQrisPaymentMethod(order: FinproOrderResponse?): String {
        val qrisTitle = order?.customer?.customerName
        return if (qrisTitle.isNotNullOrBlank()) qrisTitle.toString() else PaymentConst.QRIS
    }

    fun getPaymentNameAndIcon(category: String): MutableMap<String, Any>? {
        when (category) {
            PaymentHistory.TYPE_PAYMENT_IN -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_payment_in,
                    TEXT_MESSAGE to R.string.label_bill
                )
            }
            PaymentHistory.TYPE_PAYMENT_OUT -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_payment_out,
                    TEXT_MESSAGE to R.string.pembayaran
                )
            }
            PaymentHistory.TYPE_SALDO_IN -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_saldo_in_new,
                    TEXT_MESSAGE to R.string.topup_saldo
                )
            }
            PaymentHistory.TYPE_SALDO_REFUND -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_saldo_in_new,
                    TEXT_MESSAGE to R.string.incoming_saldo_refund
                )
            }
            PaymentHistory.TYPE_SALDO_OUT -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_saldo_out_new,
                    TEXT_MESSAGE to R.string.saldo_out
                )
            }
            CATEGORY_PULSA -> {
                return mutableMapOf(ICON_RES to R.drawable.ic_pulsa, TEXT_MESSAGE to R.string.pulsa)
            }
            CATEGORY_LISTRIK -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_electricity,
                    TEXT_MESSAGE to R.string.token_listrik
                )
            }
            CATEGORY_PULSA_POSTPAID -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_pascabayar,
                    TEXT_MESSAGE to R.string.pulsa_postpaid_title
                )
            }
            CATEGORY_EWALLET -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_ewallet,
                    TEXT_MESSAGE to R.string.ewallet
                )
            }
            CATEGORY_PAKET_DATA -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_paket_data,
                    TEXT_MESSAGE to R.string.packet_data_title
                )
            }
            CATEGORY_VOUCHER_GAME -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_voucher_game,
                    TEXT_MESSAGE to R.string.voucher_game1
                )
            }
            CATEGORY_PLN_POSTPAID -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_listrik_postpaid,
                    TEXT_MESSAGE to R.string.listrik_postpaid
                )
            }
            CATEGORY_BPJS -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_insurance,
                    TEXT_MESSAGE to R.string.bpjs
                )
            }
            CATEGORY_PDAM -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_water_works,
                    TEXT_MESSAGE to R.string.title_pdam
                )
            }
            CATEGORY_MULTIFINANCE -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_multifinance,
                    TEXT_MESSAGE to R.string.title_multifinance
                )
            }
            CATEGORY_INTERNET_DAN_TV_CABLE -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_internet_dan_tv_cable,
                    TEXT_MESSAGE to R.string.title_internet_dan_tv_cable
                )
            }
            CATEGORY_VEHICLE_TAX -> {
                return mutableMapOf(
                    ICON_RES to R.drawable.ic_vehicle_tax,
                    TEXT_MESSAGE to R.string.title_vehicle_tax
                )
            }
        }
        return null
    }

    val ppobList = mutableListOf(
        PpobProductModel(R.drawable.ic_pulsa, R.string.pulsa, false, CATEGORY_PULSA),
        PpobProductModel(
            R.drawable.ic_electricity,
            R.string.token_listrik,
            false,
            CATEGORY_LISTRIK
        ),
        PpobProductModel(
            R.drawable.ic_listrik_postpaid,
            R.string.postpaid_listrik,
            false,
            CATEGORY_PLN_POSTPAID
        ),
        PpobProductModel(
            R.drawable.ic_ewallet,
            R.string.top_up_ewallet,
            false,
            CATEGORY_EWALLET
        ),
        PpobProductModel(
            R.drawable.ic_paket_data,
            R.string.data_package,
            false,
            CATEGORY_PAKET_DATA
        ),
        PpobProductModel(
            R.drawable.ic_voucher_game,
            R.string.voucher_game,
            false,
            CATEGORY_VOUCHER_GAME
        ),
        PpobProductModel(R.drawable.ic_insurance, R.string.bpjs, false, CATEGORY_BPJS),
        PpobProductModel(
            R.drawable.ic_water_works,
            R.string.water_works,
            true,
            CATEGORY_PDAM
        ),
        PpobProductModel(
            R.drawable.ic_multifinance,
            R.string.multifinance,
            true,
            CATEGORY_MULTIFINANCE
        ),
        PpobProductModel(
            R.drawable.ic_internet_dan_tv_cable,
            R.string.internet_dan_tv_cable,
            true,
            PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE
        ),
        PpobProductModel(
            R.drawable.ic_vehicle_tax,
            R.string.title_vehicle_tax,
            true,
            PpobConst.CATEGORY_VEHICLE_TAX
        ),
        PpobProductModel(
            R.drawable.ic_pascabayar,
            R.string.postpaid_pulsa,
            false,
            CATEGORY_PULSA_POSTPAID
        )
    )

    val promotionsList = mutableListOf(
            PpobProductModel(
                    R.drawable.ic_reminder,
                    R.string.bill_reminder,
                    true,
                    PpobConst.CATEGORY_REMINDER
            ),
            PpobProductModel(
                    R.drawable.ic_set_selling_price,
                    R.string.ppob_set_sellling_price,
                    true,
                    PpobConst.CATEGORY_SET_SELLING_PRICE
            ), PpobProductModel(
            R.drawable.ic_poster_promotions,
            R.string.poster_promotions,
            true,
            PpobConst.CATEGORY_PROMOTIONS
    )
    )

    fun getPpobPromotionsList(): List<PpobProductModel> {
        return if (PaymentPrefManager.getInstance().shouldShowRemindersOptions()) {
            promotionsList
        } else {
            promotionsList.filter { it.category != PpobConst.CATEGORY_REMINDER }
        }
    }

    /**
     * Returns blacklisted book name coming in the API response after parsing.
     */
    fun parseBlacklistedBookName(errors: String): String? {
        return try {
            JSONObject(errors).get("blacklisted_book_name") as String?
        } catch (ex: Exception) {
            null
        }
    }

    /**
     * Returns true if bank supports transaction for the passed refund amount.
     * If refundAmount is null, this check is omitted and true is returned.
     * maximumLimit == -1 means there is no upper limit.
     */
    fun isSupportedForRefund(refundAmount: Double?, bank: RefundBankAccount?): Boolean {
        return Utilities.safeLet(
            refundAmount, bank?.maximumLimit, bank?.minimumLimit
        ) { amount, maxLimit, minLimit ->
            if (maxLimit == -1.0) {
                amount >= minLimit
            } else {
                amount in minLimit..maxLimit
            }
        } ?: true
    }

    /**
     * Returns filtered list of banks that can process the required amount.
     */
    fun getFilteredBanks(banks: List<Bank>, amount: Double): List<Bank> {
        return banks.filter {
            Utilities.safeLet(
                amount, it.maximumLimit, it.minimumLimit
            ) { amount, maxLimit, minLimit ->
                if (maxLimit == -1.0) {
                    amount >= minLimit
                } else {
                    amount in minLimit..maxLimit
                }
            } ?: true
        }
    }

    /**
     * A common utility method to handle payment re-directions.
     */
    fun handlePaymentsRedirection(
        context: Context, fm: FragmentManager, entryPoint: String, featureKey: String?,
        entrySource: String?
    ) {
        when (featureKey) {
            PaymentConst.QRIS_HANDLING_KEY -> {
                val entrySource = entrySource ?: AnalyticsConst.QRIS_ICON
                handleQrisRedirection(context, fm, entryPoint, entrySource)
            }
        }
    }

    /**
     * If both kyb + matching is enabled -> redirect to v3 which will be qris/start
     * If only matching is enabled -> redirect to matching/start
     * Else redirect to oldqris/start
     */
    fun getQrisWebUrl(): String {
        return when {
            RemoteConfigUtils.getPaymentConfigs().enableNameMatching.isTrue
                    && RemoteConfigUtils.getPaymentConfigs().enableKyb.isTrue ->
                RemoteConfigUtils.getPaymentConfigs().qrisWebUrlV3
            RemoteConfigUtils.getPaymentConfigs().enableNameMatching.isTrue ->
                RemoteConfigUtils.getPaymentConfigs().qrisWebUrlMatching
            else -> RemoteConfigUtils.getPaymentConfigs().qrisWebUrl
        }
    }

    /**
     * If both kyb + matching is enabled -> redirect to v3 which will be qris/form
     * If only matching is enabled -> redirect to matching/form
     * Else redirect to oldqris/form
     */
    fun getQrisFormUrl(): String {
        return when {
            RemoteConfigUtils.getPaymentConfigs().enableNameMatching.isTrue
                    && RemoteConfigUtils.getPaymentConfigs().enableKyb.isTrue ->
                RemoteConfigUtils.getPaymentConfigs().qrisFormUrlV3
            RemoteConfigUtils.getPaymentConfigs().enableNameMatching.isTrue ->
                RemoteConfigUtils.getPaymentConfigs().qrisFormUrlMatching
            else -> RemoteConfigUtils.getPaymentConfigs().qrisFormUrl
        }
    }

    /**
     * This utility method will take care of the current status of user's application and
     * will redirect or show proper view depending on the current QRIS status.
     * QRIS can be in following states
     * 1. Not whitelisted QRIS user - Ideally it shouldn't reach here, so we can ignore this click.
     * 2. User is whitelisted but haven't submitted the QRIS application - redirect to QRIS mweb
     * 3. QRIS is PENDING (finalStatus != null)
     *  3a. QRIS or KYC status is pending -> Redirect to QRIS mweb
     *  3ba. QRIS is active + already approved QRIS -> Redirect to QrisDetailsActivity
     *  3bb. QRIS is active + new user -> Redirect to QRIS mweb
     *  3bc. QRIS is active + old flow -> Show  SwitchToQrisBookBottomSheet
     * 4. QRIS is VERIFIED
     *  4a. QRIS book is active -> Redirect to QrisDetailsActivity
     *  4b. QRIS book is different -> Show SwitchToQrisBookBottomSheet
     * 5. QRIS is REJECTED
     *  5aa. QRIS book is active but name matching failed or kyb failed -> Redirect to QrisDetailsActivity
     *  5ab. QRIS book is different -> Show SwitchToQrisBookBottomSheet
     *  5b. Name matching is rejected -> Redirect to QRIS mweb
     *  5c. QRIS data is rejected -> Redirect to QRIS mweb
     *  5d. KYC data is rejected -> Redirect to KYC mweb
     */
    private fun handleQrisRedirection(
        context: Context, fm: FragmentManager, entryPoint: String, entrySource: String
    ) {
        val qrisInfo = PaymentPrefManager.getInstance().getQrisInfo()
        // entry_point prop is source of click i.e. qris_banner, qris_icon, qris_status
        // location prop is screen name i.e. pembayaran, homepage etc
        val qrisWebUrl = getQrisWebUrl()
        val qrisUrl = "${qrisWebUrl}?entry_point=$entrySource&location=$entryPoint"

        /**
         * Check if QRIS is discontinued
         */
        if (RemoteConfigUtils.getPaymentConfigs().isQrisDiscontinued.isTrue) {
            // Check if data is already saved in shared preferences
            PaymentPrefManager.getInstance().getQrisGroupData()?.let {
                if (it.status == WhitelistGroupStatus.ENABLED) {
                    context.startActivity(WebviewActivity.createIntent(context, qrisUrl, ""))
                    return
                }
            }
        }

        // Case 2: haven't submitted the QRIS
        if (qrisInfo.finalStatus == null) {
            val intent = WebviewActivity.createIntent(context, qrisUrl, "")
            context.startActivity(intent)
            return
        }

        val activeBook = SessionManager.getInstance().businessId
        when (qrisInfo.finalStatus) {
            // Case 5: QRIS is REJECTED
            // If name matching status is rejected, final status will be rejected.
            QrisAndKycStatus.REJECTED.name -> {
                when {
                    qrisInfo.matchingStatus == NameMatchingStatus.FAILED_VERIFICATION.name
                            || qrisInfo.kybStatus == VerificationStatus.FAILED_VERIFICATION.name -> {
                        // 5a. If this is an qris approved user but name matching failed while
                        // updating the bank account -> redirect to QRIS detail
                        // or kyb failed -> redirect to qris detail
                        redirectToQrIfVerified(
                            context, qrisInfo, activeBook, fm, entryPoint, qrisUrl
                        )
                    }
                    qrisInfo.qrisStatus == VerificationStatus.FAILED_VERIFICATION.name -> {
                        // 5c. QRIS is rejected
                        val intent = WebviewActivity.createIntent(context, qrisUrl, "")
                        context.startActivity(intent)
                    }
                    qrisInfo.kycStatus == VerificationStatus.FAILED_VERIFICATION.name -> {
                        // 5d. KYC is rejected
                        val url = "${getQrisWebUrl()}?onlyKyc=true"
                        val intent = WebviewActivity.createIntent(context, url, "")
                        context.startActivity(intent)
                    }
                    else -> {
                        // 5e. Ideally shouldn't reach here but in case rejection is due to
                        // some unknown reason
                        val intent = WebviewActivity.createIntent(context, qrisUrl, "")
                        context.startActivity(intent)
                    }
                }
            }
            // Case 4: QRIS is VERIFIED
            QrisAndKycStatus.VERIFIED.name -> {
                redirectToQrIfVerified(
                    context, qrisInfo, activeBook, fm, entryPoint, qrisUrl
                )
            }
            // Case 3: QRIS is PENDING
            else -> {
                when {
                    // Case 3a -> When QRIS & KYC status is verified
                    qrisInfo.isApprovedQrisUser() -> {
                        redirectToQrIfVerified(
                            context, qrisInfo, activeBook, fm, entryPoint, qrisUrl
                        )
                    }
                    else -> {
                        // Case 3aa -> When pending due to QRIS or KYC status + new flow
                        val intent = WebviewActivity.createIntent(context, qrisUrl, "")
                        context.startActivity(intent)
                    }
                }
            }
        }
    }

    /**
     * We allow user to see QRIS only if they have KYC and QRIS approved and have a QR code
     */
    private fun redirectToQrIfVerified(
        context: Context, qrisInfo: QrisInfoSubset, activeBook: String, fm: FragmentManager,
        entryPoint: String, qrisUrl: String
    ) {
        if (qrisInfo.isApprovedQrisUser()) {
            if (activeBook == qrisInfo.qrisBookId) {
                context.startActivity(
                    QrisActivity.createIntent(context, null, entryPoint)
                )
            } else {
                showBookSwitchBottomSheet(qrisInfo, fm, entryPoint)
            }
        } else {
            context.startActivity(WebviewActivity.createIntent(context, qrisUrl, ""))
        }
    }

    /**
     * A utility method that shows SwitchToQrisBookBottomSheet if required info is available
     */
    private fun showBookSwitchBottomSheet(
        qrisInfo: QrisInfoSubset,
        fm: FragmentManager,
        entryPoint: String
    ) {
        Utilities.safeLet(qrisInfo.qrisBookName, qrisInfo.qrisBookId) { bookName, bookId ->
            SwitchToQrisBookBottomSheet.createInstance(bookName, bookId, entryPoint)
                .show(fm, "qris")
        }
    }

    /**
     * Utility method to switch to another book.
     */
    fun switchBook(bookId: String, context: Context) {
        SessionManager.getInstance().businessId = bookId
        SessionManager.getInstance().appState = AppConst.APP_STATE_ALL_DELETED
        MainActivity.startActivityAndClearTop(context)
        try {
            (context as AppActivity).finish()
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
        }
    }

    /**
     * A utility method to sync up bank accounts of the customers of this user.
     * This fetches customers from the local DB that must have been updated after login and then
     * fetch bank accounts of each customer in a loop from the payment service.
     *
     * This is a persistent immediate work, so using WorkManger as recommended solution
     * by Android https://developer.android.com/guide/background#immediate-work
     */
    fun syncCustomerBankAccounts(context: Context, bookId: String) {
        val shouldSync = PaymentPrefManager.getInstance().shouldSyncBankAccounts(bookId)
        if (!shouldSync) return
        val syncWorker = OneTimeWorkRequest.Builder(BankAccountSyncWorker::class.java)
            .setInputData(workDataOf(BankAccountSyncWorker.BOOK_ID to bookId))
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                5 * 60 * 1000L, // 5 minute.
                TimeUnit.MILLISECONDS
            )
            .addTag("BankAccountSyncWorker")
            .build()
        WorkManager.getInstance(context)
            .enqueueUniqueWork("bank_accounts_sync", ExistingWorkPolicy.KEEP, syncWorker)
    }

    /**
     * Returns true if error is raised on transaction count limit and remaining_transaction_count
     * is depleted i.e. is 0
     * Also returns true if there is any other validation error.
     * This utility method is used from payments tab's create payment CTA, to show the error
     * only when KYC becomes mandatory.
     */
    fun shouldShowLimitError(errors: String): Boolean {
        try {
            val jsonErrors = JSONObject(errors)
            val limitType = (jsonErrors["data"] as JSONObject?)?.get("limit_name") as String?
            if (limitType == PaymentLimitsBottomSheet.TRANSACTION_COUNT_LIMIT) {
                val remainingCount =
                    (jsonErrors["data"] as JSONObject?)?.get("remaining_transaction_count") as Int?
                if (remainingCount ?: 0 <= 0) {
                    return true
                }
                return false
            }
            return true
        } catch (ex: JSONException) {
            FirebaseCrashlytics.getInstance().recordException(ex)
            return false
        }
    }

    /**
     * Event has to be slightly different then what we receive from backend to keep it consistent
     * with backend event logging.
     */
    fun getQrisStatusForEvents(qrisInfo: QrisResponse): String? {
        return when (qrisInfo.finalStatus) {
            QrisAndKycStatus.INITIAL.name -> PaymentConst.INITIAL_LOWERCASE
            QrisAndKycStatus.PENDING.name -> PaymentConst.PENDING_LOWERCASE
            QrisAndKycStatus.VERIFIED.name -> PaymentConst.APPROVED_LOWERCASE
            QrisAndKycStatus.REJECTED.name -> {
                "${PaymentConst.REJECTED_LOWERCASE} (${getRejectionReason(qrisInfo)})"
            }
            else -> qrisInfo.finalStatus
        }
    }

    /**
     * Event has to be slightly different then what we receive from backend to keep it consistent
     * with backend event logging.
     */
    fun getKybStatusForEvents(verificationStatus: String): String {
        return when (verificationStatus) {
            VerificationStatus.UNVERIFIED.name -> PaymentConst.INITIAL_LOWERCASE
            VerificationStatus.PENDING_VERIFICATION.name -> PaymentConst.PENDING_LOWERCASE
            VerificationStatus.VERIFIED.name -> PaymentConst.APPROVED_LOWERCASE
            VerificationStatus.FAILED_VERIFICATION.name -> PaymentConst.REJECTED_LOWERCASE
            else -> verificationStatus
        }
    }

    fun areChangesAllowedInQrisBook(qrisInfo: QrisInfoSubset): Boolean {
        return if (qrisInfo.finalStatus.equals(QrisAndKycStatus.REJECTED.name)) {
            when {
                qrisInfo.matchingStatus == NameMatchingStatus.FAILED_VERIFICATION.name -> false
                else -> true
            }
        } else {
            false
        }
    }

    /**
     * Returns reason for qris rejection
     */
    private fun getRejectionReason(qrisInfo: QrisResponse): String {
        return when {
            qrisInfo.qrisStatus == VerificationStatus.FAILED_VERIFICATION.name
                    && qrisInfo.kycStatus == VerificationStatus.FAILED_VERIFICATION.name ->
                // Both KYC and QRIS is rejected
                AnalyticsConst.KYC_AND_QRIS_REJECTED
            qrisInfo.kycStatus == VerificationStatus.FAILED_VERIFICATION.name ->
                // KYC is rejected, QRIS is pending/verified
                AnalyticsConst.KYC_REJECTED
            qrisInfo.qrisStatus == VerificationStatus.FAILED_VERIFICATION.name ->
                // QRIS is rejected, KYC is pending/verified
                AnalyticsConst.QRIS_REJECTED
            qrisInfo.matchingStatus == NameMatchingStatus.FAILED_VERIFICATION.name ->
                // Name matching failure
                AnalyticsConst.NAME_MATCHING_FAILED
            else -> ""
        }
    }

    /**
     * Returns true if QRIS rejection widget is visible since last 2 weeks
     */
    fun shouldHideQrisRejectedWidget(): Boolean {
        val rejectedTs = PaymentPrefManager.getInstance().getQrisRejectedTs() ?: return false
        val twoWeeksMillis = 2 * 7 * 24 * 60 * 60 * 1000
        return System.currentTimeMillis() - rejectedTs > twoWeeksMillis
    }

    /**
     * Returns reason string for event logging that is mapped from
     * the error code received from the backend
     */
    fun getReason(reasonCode: String?): String {
        return when (reasonCode) {
            "101" -> "system down"
            "102" -> "lower min"
            "103" -> "higher max"
            "104" -> "not verified kyc"
            "105" -> "not available"
            else -> "other"
        }
    }

    /**
     * Returns date when the cashback will be processed in "10 Desember" format
     * Cashbacks are processed T+1 date, so if we calculate processing date from lastModifiedDate
     * If lastModifiedDate is not present, we fallback to createdDate
     */
    fun getCashbackProcessingDate(cashbackResponse: CashbackResponse): String {
        val dateToParse = cashbackResponse.lastModifiedDate ?: cashbackResponse.createdDate
        if (dateToParse != null) {
            val date = DateTimeUtils.getDateFromPayment(dateToParse)
            val calendar = Calendar.getInstance()
            calendar.time = date
            // Adding T+1 date to created or last modified date
            calendar.add(Calendar.DATE, 1)
            val localDateFormat = SimpleDateFormat("dd MMMM", Locale("ID", "id"))
            return localDateFormat.format(calendar.time)
        }
        return ""
    }

    /**
     * Provides status text to show for any transaction for Paid status only.
     * Since Payment In transactions have some condition that affects the status text,
     * this utility method was added to separate out the logics from the activity.
     */
    fun getStatusTextForCompletedTrx(
        context: Context, type: String?, order: FinproOrderResponse
    ): String {
        return when (type) {
            PaymentHistory.TYPE_PAYMENT_IN -> {
                // Payment In doesn't have a refund state but status has to show that it is
                // refunded if there was any failure earlier, we get this info from
                // invalid_disbursable_accounts from FinproOrderResponse class
                if (order.invalidDisburableAccounts?.isNotEmpty().isTrue) {
                    context.getString(R.string.automatic_refund_sucess_status)
                } else {
                    context.getString(R.string.success_label)
                }
            }
            else -> context.getString(R.string.success_label)
        }
    }

    /**
     * Returns key for previously selected category ID
     */
    fun getKeyForSelectedCategoryId(paymentType: Int) =
        PaymentPrefManager.PAYMENT_CATEGORY_ID_SELECTED + SessionManager.getInstance().businessId +
                if (paymentType == PaymentConst.TYPE_PAYMENT_IN) {
                    PaymentConst.PaymentRequest
                } else {
                    PaymentConst.DisbursementRequest
                }

    enum class KycCheckType {
        KTP, FACE_COMPARISON
    }

    fun isSelfieMatchFailure(kycStatus: KycStatusResponse): Boolean {
        return kycStatus.basicKyc?.checks?.firstOrNull { it.checkType == KycCheckType.FACE_COMPARISON.name }
            ?.failureReason.isNotNullOrBlank()
    }


    /**
     * Maps integer payment type to string constants
     */
    fun getPaymentTypeMapping(type: Int): String {
        return when (type) {
            PaymentConst.TYPE_PAYMENT_IN -> PaymentHistory.TYPE_PAYMENT_IN
            PaymentConst.TYPE_PAYMENT_OUT -> PaymentHistory.TYPE_PAYMENT_OUT
            else -> ""
        }
    }

    /**
     * Returns warning message to be shown for the receiver bank
     * Following 4 parameters are configurable from the backend
     * 1. Receiver's bank code - for which banks warning has to be shown
     * 2. Payment Types (IN, OUT, etc)
     * 3. Amount - If warning has to be shown for specific amount range
     * 4. Message - Warning message that has to be show to the user.
     */
    fun getWarningForReceiver(
        receiverBankCode: String, paymentType: String, amount: Double
    ): ReceiverWarning? {
        val warnings = RemoteConfigUtils.getPaymentConfigs().receiverChannelWarnings
        // There are 2 lists, applicableBankCodes and applicableBankCodesExcept
        // We need to show warning if bank code exists in applicableBankCodes
        // OR it doesn't exist in applicableBankCodesExcept
        // Check if warnings are present for receiver Bank Code
        return warnings?.firstOrNull { it.applicableBankCodes?.contains(receiverBankCode).isTrue }
            ?.shouldShowWarning(paymentType, amount)
            ?: run {
                // We check for expect condition, if receiver bank code is present in except bank list,
                // we don't show any warning, otherwise we try to parse the warning.
                warnings?.firstOrNull {
                    !it.applicableBankCodesExcept.isNullOrEmpty() &&
                            !it.applicableBankCodesExcept.contains(receiverBankCode).isTrue
                }?.shouldShowWarning(paymentType, amount)
            }
    }

    private fun ReceiverWarning.shouldShowWarning(
        paymentType: String, amount: Double
    ): ReceiverWarning? {
        // Check if warning is for selected payment type
        return if (this.paymentTypes?.contains(paymentType).isTrue) {
            // If max amount is null, we show warning for all the amounts
            // It is done this way to avoid having to enter a very large number for max amount
            if (this.maxAmount == null && this.minAmount != null) return if (amount >= this.minAmount) this else null
            // If both min and max amounts are present, we check for the entered amount
            Utilities.safeLet(this.minAmount, this.maxAmount) { minAmount, maxAmount ->
                if (amount in minAmount..maxAmount) this else null
            }
        } else null
    }

    /**
     * Replaces variables in the warning message.
     */
    fun parseWarningMessage(message: String, amount: Double): String {
        return if (message.indexOf("\$amount") != -1) {
            message.replace("\$amount", Utility.formatAmount(amount))
        } else message
    }

    /**
     * Returns timestamp after adding processing time to created_at timestamp
     * eta is expected to be in HH:MM format and it's the difference in time, not the absolute time
     * so 00:15 means 15 minutes and not 15 minutes past midnight
     */
    private fun getProcessingTimestamp(createdTS: String?, eta: String?): String {
        val calendar = Calendar.getInstance()
        calendar.time = DateTimeUtils.getDateFromPaymentUTC(createdTS)
        eta?.let {
            val time = it.split(":")
            val hour = time[0].toInt(0)
            val minutes = time[1].toInt(0)
            calendar.add(Calendar.HOUR, hour)
            calendar.add(Calendar.MINUTE, minutes)
        }
        val processingTS = DateTimeUtils.getTimeInUTC(calendar.time)
        return DateTimeUtils.getFormattedLocalDateTime(
            processingTS, DateTimeUtils.HH_MM
        )
    }

    /**
     * Converts eta in 24 hour format to user friendly format, e.g. 05:00 to 5 pagi
     */
    fun getUserFriendlyEta(context: Context, eta: String?, timezone: String?): String {
        val pair = parseHourAndMins(eta)
        val hour = pair.first
        val mins = pair.second
        return when (mins) {
            0 -> {
                if (hour.orNil < 12) context.getString(R.string.time_am_w_timezone, hour.toString(), timezone)
                else context.getString(R.string.time_pm_w_timezone, hour.toString(), timezone)
            }
            else -> {
                if (hour.orNil < 12) context.getString(R.string.time_am_w_timezone, "$hour:$mins", timezone)
                else context.getString(R.string.time_pm_w_timezone, "$hour:$mins", timezone)
            }
        }
    }

    /**
     * Converts eta in 24 hour format to hour and minutes format, e.g. 1:15 to 1 hour 15 minutes
     */
    fun getHoursAndMinutes(context: Context, eta: String?): String {
        val pair = parseHourAndMins(eta)
        val hour = pair.first
        val mins = pair.second
        return when (hour) {
            0 -> context.getString(R.string.time_minutes, mins.toString())
            else -> context.getString(R.string.time_hour_minutes, hour.toString(), mins.toString())
        }
    }

    private fun parseHourAndMins(eta: String?): Pair<Int, Int> {
        val time = eta?.split(":")
        val hour = time?.getOrNull(0).toInt(0)
        val mins = time?.getOrNull(1).toInt(0)
        return Pair(hour, mins)
    }

    /**
     * Calculates start and end date for date filter presets.
     * Null values are returned in case preset value isn't found and for CUSTOM_RANGE preset if
     * startDate and endDate is not found.
     *
     * Returns Pair of dates(timeInMillis)
     */
    fun getDates(dateFilterDay: DateFilter): Pair<Long?, Long?> {
        val presetVal = dateFilterDay.presetValue
        var cal = Calendar.getInstance()
        presetVal?.let {
            return when (presetVal) {
                PaymentConst.DATE_PRESET.TODAY -> {
                    Pair(cal.timeInMillis, cal.timeInMillis)
                }
                PaymentConst.DATE_PRESET.YESTERDAY -> {
                    cal.add(Calendar.DATE, -1)
                    Pair(cal.timeInMillis, cal.timeInMillis)
                }
                PaymentConst.DATE_PRESET.THIS_WEEK -> {
                    val endTime = cal.timeInMillis
                    val dayOfWeek = cal.get(Calendar.DAY_OF_WEEK)
                    //get Monday for this week, DAY_OF_WEEK is 2 for Monday
                    val minus = if (dayOfWeek == 1) -6 else 2 - dayOfWeek
                    cal.add(Calendar.DATE, minus)
                    Pair(cal.timeInMillis, endTime)
                }
                PaymentConst.DATE_PRESET.LAST_WEEK -> {
                    val dayOfWeek = cal.get(Calendar.DAY_OF_WEEK)
                    val minus = if (dayOfWeek == 1) -6 else 2 - dayOfWeek
                    val lastWeekMonday = minus - 7
                    cal.add(Calendar.DATE, lastWeekMonday)
                    val sTime = cal.timeInMillis
                    cal.add(Calendar.DATE, 6)
                    Pair(sTime, cal.timeInMillis)
                }
                PaymentConst.DATE_PRESET.THIS_MONTH -> {
                    val endTime = cal.timeInMillis
                    cal[Calendar.DAY_OF_MONTH] = 1
                    Pair(cal.timeInMillis, endTime)
                }
                PaymentConst.DATE_PRESET.LAST_MONTH -> {
                    val aCalendar = Calendar.getInstance()
                    aCalendar.add(Calendar.MONTH, -1)
                    aCalendar[Calendar.DATE] = 1
                    val sTime = aCalendar.timeInMillis
                    aCalendar[Calendar.DATE] = aCalendar.getActualMaximum(Calendar.DAY_OF_MONTH)
                    val endTime = aCalendar.timeInMillis
                    Pair(sTime, endTime)
                }
                PaymentConst.DATE_PRESET.THIS_YEAR -> {
                    val endTime = cal.timeInMillis
                    cal.set(Calendar.MONTH, 0)
                    cal.set(Calendar.DATE, 1)
                    val sTime = cal.timeInMillis
                    Pair(sTime, endTime)
                }
                PaymentConst.DATE_PRESET.LAST_YEAR -> {
                    cal.set(Calendar.MONTH, 0)
                    cal.set(Calendar.DATE, 1)
                    cal.add(Calendar.YEAR, -1)
                    val sTime = cal.timeInMillis
                    cal.set(Calendar.MONTH, 11)
                    cal[Calendar.DATE] = cal.getActualMaximum(Calendar.DAY_OF_MONTH)
                    val endTime = cal.timeInMillis
                    Pair(sTime, endTime)
                }
                PaymentConst.DATE_PRESET.LAST_SEVEN_DAYS -> {
                    val endTime = cal.timeInMillis
                    cal.add(Calendar.DATE, -6)
                    Pair(cal.timeInMillis, endTime)
                }
                PaymentConst.DATE_PRESET.LAST_TEN_DAYS -> {
                    val endTime = cal.timeInMillis
                    cal.add(Calendar.DATE, -9)
                    Pair(cal.timeInMillis, endTime)
                }
                PaymentConst.DATE_PRESET.CUSTOM_RANGE -> {
                    Pair(dateFilterDay.startDate, dateFilterDay.endDate)
                }
                PaymentConst.DATE_PRESET.ALL -> {
                    Pair(null, null)
                }
            }
        } ?: run {
            val startDate = dateFilterDay.startDays
            val endDate = dateFilterDay.endDays
            return if (startDate != null && endDate != null) {
                cal.add(Calendar.DATE, startDate)
                val sTime = cal.timeInMillis
                cal = Calendar.getInstance()
                cal.add(Calendar.DATE, endDate)
                val endTime = cal.timeInMillis
                Pair(sTime, endTime)
            } else Pair(null, null)
        }
    }

    fun getQrisProcessingInfo(context: Context, timings: DisbursementTimings): String {
        val sb = StringBuilder()
        timings.operatingHours?.let { list ->
            sb.append("<b>${context.getString(R.string.payment_during_operating_hours)}</b>")
            sb.append("<br>")
            list.distinctBy { it.eta }.forEachIndexed loop@{ index, it ->
                if (index > 0) sb.append("<br>")
                sb.append(
                    context.getString(
                        R.string.payment_forwarded_within_x,
                        getHoursAndMinutes(context, it.eta)
                    )
                )
            }
        }
        timings.nonOperatingHours?.let { list ->
            sb.append("<br><br>")
            sb.append("<b>${context.getString(R.string.payment_during_non_operating_hours)}</b>")
            sb.append("<br>")
            list.forEachIndexed { index, it ->
                if (index > 0) sb.append("<br>")
                sb.append(
                    context.getString(
                        R.string.payment_non_op_forwarded_at_x,
                        if (!it.amount.isNullOrEmpty()) "${it.amount} " else "",
                        it.requestTimestamp,
                        getUserFriendlyEta(context, it.eta, it.timezone),
                    )
                )
            }
        }
        return sb.toString()
    }

    fun shouldBeBlockedAsPerKycTier(category: String): Boolean {
        val kycTierConfig = RemoteConfigUtils.getPaymentConfigs().kycTierConfig
        val kycTierList = when(PaymentPrefManager.getInstance().getKycTier()) {
            KycTier.NON_KYC -> kycTierConfig?.featuresAllowedForNonKyc
            KycTier.ADVANCED -> {
                when (category) {
                    PaymentConst.KYC_PAYMENT_IN, PaymentConst.KYC_PAYMENT_OUT -> {
                        val feature =
                            if (category == PaymentConst.KYC_PAYMENT_IN) PaymentConst.Feature.PAYMENT_IN else PaymentConst.Feature.PAYMENT_OUT
                        return shouldBeBlockedDueToQuota(feature)
                    }
                    else -> kycTierConfig?.featuresAllowedForKycAdvanced
                }
            }
            KycTier.SUPREME -> kycTierConfig?.featuresAllowedForKycSupreme
        }
        return kycTierList?.contains(category).isFalse
    }

    /**
     * Whitelisted ADVANCED tier users have limited quota for using payments, this will check if
     * that quota is reached.
     */
    fun shouldBeBlockedDueToQuota(feature: String): Boolean {
        // Currently quota is applicable for ADVANCED tier only, so for other tier we just return false
        if (PaymentPrefManager.getInstance().getKycTier() != KycTier.ADVANCED) {
            return false
        }
        val limits = PaymentPrefManager.getInstance().getPaymentLimits()?.whitelistLimits
        return when(feature) {
            PaymentConst.Feature.PAYMENT_IN -> {
                limits?.paymentInLimits?.remainingTrxAmountLimit.orNil == 0.0
            }
            PaymentConst.Feature.PAYMENT_OUT -> {
                limits?.paymentOutLimits?.remainingTrxAmountLimit.orNil == 0.0
            }
            else -> false
        }
    }

    /**
     * ADVANCED tier users who have been whitelisted to use payments.
     */
    fun isWhitelistedUser(): Boolean {
        return PaymentPrefManager.getInstance().getPaymentLimits()?.whitelistLimits != null
    }

    fun getRemainingCountQuota(feature: String?): Int? {
        return when (feature) {
            PaymentConst.Feature.PAYMENT_IN -> {
//                PaymentPrefManager.getInstance().getPaymentLimits()?.whitelistLimits?.paymentInLimits?.remainingTrxCountLimit
                0
            }
            PaymentConst.Feature.PAYMENT_OUT -> {
                PaymentPrefManager.getInstance().getPaymentLimits()?.whitelistLimits?.paymentOutLimits?.remainingTrxCountLimit
            }
            else -> null
        }
    }

    fun showKycKybStatusBottomSheet(fragmentManager: FragmentManager, entryPoint: String) {
        val useCase = when (PaymentPrefManager.getInstance().getKycTier()) {
            KycTier.NON_KYC -> {
                if (PaymentPrefManager.getInstance().getKycStatus()?.isPending().isTrue) {
                    KycKybBottomSheet.UseCase.KYC_PENDING
                } else {
                    KycKybBottomSheet.UseCase.KYC_REQUIRED
                }
            }
            KycTier.ADVANCED -> {
                if (PaymentPrefManager.getInstance().getKybStatus()?.isPending().isTrue) {
                    KycKybBottomSheet.UseCase.KYB_PENDING
                } else {
                    KycKybBottomSheet.UseCase.KYB_REQUIRED
                }
            }
            else -> null
        }
        useCase?.let {
            KycKybBottomSheet.createInstance(it, entryPoint = entryPoint)
                .show(fragmentManager, KycKybBottomSheet.TAG)
        }
    }

    /**
     * Returns true if update is required to use the passed UseCase(feature).
     * If there is no remote config for it, returns false.
     * Also checks if this version check should be performed or not.
     * This can be used to disable version check for all the UseCases.
     *
     * It is expected that app version will be in Semantic versioning i.e. 15.8.47
     */
    fun isUpdateRequired(useCase: String): Boolean {
        if (!RemoteConfigUtils.performVersionCheck()) return false
        // Convert the versions into list of digits i.e. ["15", "8", "47"]
        val minSupportedVersion =
            RemoteConfigUtils.getMinSupportedVersions()[useCase]?.split(".")?.toTypedArray()
        val currentVersion = BuildConfig.VERSION_NAME.split("-")[0].split(".").toTypedArray()

        minSupportedVersion?.let {
            // If one of the version is longer, say 3.46.1.1,
            // we consider 0 for the smaller version for the missing numbers
            val maxLength = it.size.coerceAtLeast(currentVersion.size)
            // Compare each level from left to right in priority order of highest to lowest
            // Return true if min supported version is gt current version
            // Return false if min supported version is lt current version
            for (i in 0 until maxLength) {
                val minSupported = if (i >= minSupportedVersion.size) 0 else minSupportedVersion[i].toInt()
                val current = if (i >= currentVersion.size) 0 else currentVersion[i].toInt()
                if (minSupported > current) return true
                if (minSupported < current) return false
            }
            return false
        } ?: run {
            return false
        }
    }

    /**
     * Returns intent for handling different redirections for a bank account, i.e.
     * If the account is blocked, already used, matching failed etc.
     */
    fun getBankAccountRedirectionIntent(
        context: Context,
        urlType: UrlType, bankAccount: String? = null,
        paymentType: String, entryPoint: String
    ): Intent {
        val intent: Intent = when (urlType) {
            UrlType.APPEAL_FLOW -> {
                WebviewActivity.createIntent(
                    context,
                    RemoteConfigUtils.getPaymentConfigs().appealFlowUrl +
                            "?type=$paymentType&entryPoint=$entryPoint",
                    ""
                ).apply {
                    putExtra(WebviewActivity.APPEAL_BANK_ACCOUNT, bankAccount)
                }
            }

            UrlType.MATCHING_INFO -> {
                WebviewActivity.createIntent(
                    context,
                    RemoteConfigUtils.getPaymentConfigs().matchingInfoUrl,
                    ""
                )
            }

            UrlType.FAQ_USED_ACCOUNT -> {
                WebviewActivity.createIntent(
                    context,
                    RemoteConfigUtils.getPaymentConfigs().faqUsedAccountUrl,
                    ""
                )
            }

            UrlType.FAQ_BLOCKED_ACCOUNT -> {
                WebviewActivity.createIntent(
                    context,
                    RemoteConfigUtils.getPaymentConfigs().faqBlockedAccountUrl,
                    ""
                )
            }
        }
        return intent
    }

    /**
     * Checks if user should be prompted for pre-filling Payment Out data.
     * This checks if it has not been more than 24hours since last time user filled the data.
     * This also checks if at least Destination bank and amount is available to be pre-filled.
     */
    fun getPayOutFilledData(): PayOutFilledData? {
        val data = PaymentPrefManager.getInstance().getPayOutFilledData()
        data?.let {
            it.timestamp?.let { dataTimeStamp ->
                // Return null if it has been more than 24 hours
                val cutOffTime = System.currentTimeMillis() - DateTimeUtils.convertHoursToMillis(24)
                if (dataTimeStamp < cutOffTime) {
                    return null
                }
            } ?: run {
                // Return null if there is no timestamp
                return null
            }
            // Returns null if Destination bank is missing
            if (data.bankAccountDetail == null) {
                return null
            }
            return it
        }
        return null
    }
}