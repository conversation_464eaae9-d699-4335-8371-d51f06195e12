package com.bukuwarung.utils

import android.util.Log
import com.bukuwarung.BuildConfig
import com.bukuwarung.activities.businessdashboard.view.TransactionCategoryBreakUpColor
import com.bukuwarung.activities.card.newcard.BusinessCardDesign
import com.bukuwarung.activities.catalogproduct.data.model.CatalogCategory
import com.bukuwarung.activities.homepage.data.HomeFabConfig
import com.bukuwarung.activities.onboarding.form.CategoryOption
import com.bukuwarung.activities.onboarding.helper.onboarding.OnBoardingRedirection
import com.bukuwarung.activities.print.InvoiceDataBlock
import com.bukuwarung.activities.profile.model.ProfileTabFeedbackInfo
import com.bukuwarung.api.model.ConfigImages
import com.bukuwarung.api.model.MxMwebConfigs
import com.bukuwarung.constants.RemoteConfigConst
import com.bukuwarung.constants.RemoteConfigConst.ADD_FAVOURITE_DIALOG_CONFIG
import com.bukuwarung.constants.RemoteConfigConst.TICKER_FRAGMENT_DATA
import com.bukuwarung.data.referral.ReferralContent
import com.bukuwarung.data.referral.ReferralWebContent
import com.bukuwarung.enums.NotificationChannel
import com.bukuwarung.payments.data.model.AppTexts
import com.bukuwarung.payments.data.model.EdcConfig
import com.bukuwarung.payments.data.model.PaymentConfigs
import com.bukuwarung.payments.data.model.PpobConfig
import com.bukuwarung.payments.data.model.QrisRetryConfig
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.session.SessionManager
import com.google.firebase.ktx.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfig
import com.google.firebase.remoteconfig.ktx.remoteConfigSettings
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.util.Locale

object RemoteConfigUtils {
    private const val TAG = "RemoteConfigUtils"

    private const val VALUE_N = "value_n"
    private const val SALES_ONBOARDING_THRESHOLD = "sales_onboarding_threshold"
    private const val FORCE_RELOAD_WEBVIEW = "forced_reload_webview"
    private const val VALUE_M = "value_m"
    private const val OTP_ASSIST_URL = "otp_assist_url"
    private const val ASSIST_URL = "assist_url"
    private const val ASSIST_URL_APPEND_BOOK = "assist_url_append_book"
    private const val UNPAID_TRANSACTION_SUPPORT = "unpaid_transaction_support"
    private const val VALUE_PROFILE_VISIT_CONFIG_COUNT = "profile_visit_config_count"
    private const val GAMIFY_DIALOG_STATUS = "gamify_dialog_status"
    private const val INVOICE_VISIBILITY_THRESHOLD = "invoice_visibility_threshold"
    private const val ALLOWED_INVOICE_IGNORE_TRANSACTION = "allowed_invoice_ignore_count"
    private const val FORCED_INVOICE_VISIBILITY_THRESHOLD = "forced_invoice_visibility_threshold"
    private const val FORCED_INVOICE_UTANG_VISIBILITY_THRESHOLD =
        "forced_invoice_utang_visibility_threshold"
    private const val INVOICE_POWER_USER_TRANSACTION = "invoice_power_user_transaction"

    private const val WELCOME_SCREENS = "welcome_screens"
    private const val WELCOME_SCREEN_SLIDE_DURATION = "welcome_screen_slide_duration"

    private const val SHOW_UTANG_SUCCESS_ANIMATION = "show_utang_success_animation"
    private const val SHOW_LOYALTY_LAYOUT = "show_loyalty_layout"
    private const val SHOW_INCENTIVE_LAYOUT = "show_incentive_layout"
    private const val SHOW_SALDO_BONUS = "show_saldo_bonus"
    private const val SHOW_CASH_SUCCESS_ANIMATION = "show_cash_success_animation"
    private const val TRX_SUCCESS_MESSAGE = "trx_success_message"
    private const val REFERRAL_ELIGIBLE_LEVEL = "referral_eligible_level"
    private const val BUSINESS_PROFILE_VARIANT = "business_profile_variant"
    private const val SHOW_PROFILE_SETUP_DIALOG = "show_profile_setup_dialog"
    private const val SHOW_DISBURSEMENT_CONFIRMATION_DIALOG = "show_bank_details_popup"

    private const val SHOW_ALL_WHITE = "show_all_white"
    private const val HIDE_CUSTOMER_NAME = "hide_customer_name"
    private const val SEND_SMS_UTANG = "send_sms_utang"
    private const val SEND_SMS_TRANSACTION = "send_sms_transaction"

    /**
     * 1 Alfamart
     * 2 Sticker
     * 3 RPU Experiment
     * 4 Badge
     * */
    private const val STREAK_VARIANT = "streak_variant"

    private const val UTANG_ENTRY_OLD = "show_utang_entry_old"

    private const val SHOW_EXIT_DIALOG = "show_exit_dialog"

    private const val BULK_TRANSAKSI_THRESHOLD = "bulk_transaksi_threshold"
    private const val IS_BULK_TRANSAKSI_ENABLED = "is_bulk_transaksi_enabled"

    private const val ON_BOARDING_VARIANT =
        "on_boarding_variant" // to decide between old and new onBoarding
    private const val NEW_ON_BOARDING_FLOW_FLAG =
        "new_on_boarding_flow_flag" // to decide between old and new onBoarding

    private const val ON_BOARDING_FULL_QUESTION =
        "on_boarding_full_question" // 2 or 4 question for old onBoarding
    private const val ON_BOARDING_FORMS = "on_boarding_forms" // new onBoarding with webview
    private const val USER_ONBOARDING_FORMS = "user_onboarding_forms" // new onBoarding with native

    private const val ON_BOARDING_URL = "on_boarding_url" // new onBoarding with webview
    private const val ON_BOARDING_REDIRECTION =
        "on_boarding_redirection" // new onBoarding with webview

    private const val SHOW_ASSIST_PAGE = "show_assist_page"
    private const val SHOW_OLD_PAYMENT_OUT_UI = "show_old_payment_out_ui"

    private const val OTP_CHANNEL = "otp_channel"
    private const val SHOW_NEW_LOGIN_SCREEN = "show_new_login_screen"

    // help icon for utang/transaksi
    private const val SHOW_HELP_ICON = "show_help_icon"
    private const val REDIRECT_HELP_ICON = "redirect_help_icon"
    private const val SHOW_APP_SHORTCUTS = "show_app_shortcuts"

    private const val IS_PAYMENT_CATEGORY_MANDATORY = "is_payment_category_mandatory"

    private const val SHOW_RECENTS_AND_FAVOURITES = "show_recents_and_favourites"

    private const val PAYMENT_BANNER_URL = "payment_banner_url"
    private const val HIDE_CATEGORY = "hide_category"
    private const val HIDE_NOTES = "hide_notes"
    private const val HIDE_INVENTORY = "hide_inventory"
    private const val PROFILE_PINS_COLUMNS = "profile_pin_rows"

    private const val PROFILE_PINS = "profile_pins"
    private const val USER_PROFILE_OPTIONS = "user_profile_options"
    private const val NOTES_MISSION_STEPS = "notes_mission_steps"

    private const val SHOW_PREVIOUS_TRANSAKSI = "show_previous_transaksi"

    private const val STICKER_REWARD_DEEPLINK = "sticker_reward_deeplink"
    private const val POSTER_REWARD_DEEPLINK = "poster_reward_deeplink"
    private const val STATUS_REWARD_DEEPLINK = "status_reward_deeplink"
    private const val REFERRAL_URL = "referral_url"
    private const val DAILY_UPDATE_URL = "daily_update_url"
    private const val BUSINESS_PROFILE_URL = "business_profile_url"
    private const val DAILY_UPDATE_ACTIVATION_MINUTE = "daily_update_activation_minute"
    private const val DAILY_UPDATE_ACTIVATION_HOUR = "daily_update_activation_hour"
    private const val DAILY_UPDATE_MINIMUM_TRANSACTION_NUMBER =
        "daily_update_minimum_transaction_number"
    private const val USER_COUNT_ON_WELCOME_SCREEN = "user_count_on_welcome_screen"

    /**
     * POS configs
     */
    private const val SHOW_POS_ONBOARDING = "show_pos_onboarding"
    private const val POS_NON_CASH_ENABLE = "pos_non_cash_enable"
    private const val POS_PAYMENT_WALLETS = "pos_payment_wallets"

    private const val FAVORITE_CUSTOMER_ENABLED = "favorite_customer_enabled"
    private const val PINNED_FAVORITE_CUSTOMERS_LIMIT = "pinned_favorite_customers_limit"
    private const val LIST_FAVORITE_CUSTOMERS_LIMIT = "list_favorite_customers_limit"
    private const val RECENT_CUSTOMERS_LIMIT = "recent_customers_limit"

    private const val ENABLE_HAMBURGER_MENU_BANNER = "enable_hamburger_menu_banner"
    private const val HAMBURGER_MENU_BANNER_URL = "hamburger_menu_banner_url"
    private const val LANDING_DIALOG_BANNER = "landing_dialog_banner"
    private const val LANDING_DIALOG_HEADER = "landing_dialog_header"
    private const val LANDING_DIALOG_BODY = "landing_dialog_body"
    private const val LANDING_DIALOG_BUTTON_TEXT = "landing_dialog_button_text"
    private const val PROFILE_TAB_NORMAL_BANNER_URL_NEW = "profile_tab_normal_banner_url_new"
    private const val ADD_FAVOURITE_DIALOG = "add_favourite_dialog"
    private const val LIGHTNING_FAST_SALDO_TEXT = "lightning_fast_saldo_text"
    private const val USE_LIGHTNING_FAST_SALDO = "use_lightning_fast_saldo"
    private const val PROFILE_TAB_LENDING_BANNER_URL_NEW = "profile_tab_lending_banner_url_new"
    private const val PULSA_POSTPAID_WARNING_LINK = "pulsa_postpaid_warning_link"
    private const val REFERRAL_TNC_BULLET_POINT = "referral_tnc_bullet_point"
    private const val REFERRAL_TNC_HEADER = "referral_tnc_header"
    private const val HAMBURGER_MENU_LANDING = "hamburger_menu_landing"
    private const val PAYMENT_LANDING_BOTTOM_SHEET = "payment_landing_bottom_sheet"
    private const val REFERRAL_WA_MESSAGE = "referral_wa_message"
    private const val REFERRAL_IMAGE_URL = "referral_image_url"

    private const val PROFILE_BANNER_AUTOSCROLL_TIME = "profile_banner_autoscroll_time"
    private const val MINIMUM_PAYMENT_AMOUNT = "minimum_payment_amount"
    private const val SALDO_MIN_TOPUP_AMOUNT = "saldo_min_topup_amount"
    private const val SALDO_MIN_TOPUP_AMOUNT_VALUE = 10000.0
    private const val MINIMUM_PAYMENT_OUT_AMOUNT = "minimum_payment_out_amount"

    private const val QRIS_RETRY_CONFIGS = "qris_retry_configs"
    private const val PAYMENT_CONFIGS = "payment_configs"
    private const val PPOB_CONFIG = "ppob_config"
    private const val EDC_CONFIG = "edc_config"
    private const val APPLY_NEW_COMPRESSION = "apply_new_compression"
    private const val PERFORM_VERSION_CHECK = "perform_version_check"
    private const val MIN_SUPPORTED_VERSIONS = "min_supported_versions"
    private const val PHYSICAL_VISIT_CITIES = "physical_visit_cities"
    private const val USE_NEW_ORDER_INVOICE = "use_new_order_invoice"

    private const val MX_MWEB_CONFIGS = "mx_mweb_configs"

    private const val NEW_BUSINESS_CARD_ENABLED = "new_business_card_enabled"
    private const val NEW_BUSINESS_CARD_DESIGN = "new_business_card_design"
    private const val BUSINESS_CARD_LOCKED_DESIGN = "business_card_locked_design"

    private const val SOCIAL_MEDIA_POSTER_DATA = "social_media_poster_data"
    private const val SOCIAL_MEDIA_STATUS_DATA = "social_media_status_data"
    private const val REFERRAL_CONTACT_LIST_ENABLED = "referral_contact_list_enabled"

    private const val STOCK_ON_OFF_TOGGLE = "stock_toggle_july"

    private const val KYC_LAYOUT_MIN_THRESHOLD = "kyc_layout_min_threshold"

    private const val REFERRAL_FLOATING_BUTTON = "referral_floating_button"
    private const val REFERRAL_FLOATING_BUTTON_VISIBILITY_THRESHOLD =
        "referral_floating_button_visibility_threshold"
    private const val REFERRAL_FLOATING_BUTTON_REDIRECTION = "referral_floating_button_redirection"
    private const val REFERRAL_FLOATING_BUTTON_REDIRECTION_TYPE =
        "referral_floating_button_redirection_type"

    private const val TRX_BLANK_SCREEN_VARIANT = "trx_blank_screen_variant"
    const val TRX_BLANK_SCREEN_VARIANT_SHOW_COACHMARK = "trx_blank_screen_variant_show_coachmark"
    const val TRX_BLANK_SCREEN_VARIANT_SHOW_VIDEO = "trx_blank_screen_variant_show_video"
    const val TRX_BLANK_SCREEN_VARIANT_SHOW_COACHMARK_AND_VIDEO =
        "trx_blank_screen_variant_show_coachmark_and_video"
    const val TRX_BLANK_SCREEN_VARIANT_NO_COACHMARK_AND_VIDEO =
        "trx_blank_screen_variant_no_coachmark_and_video"
    const val TRX_BLANK_SCREEN_EXPERIMENT_VIDEO_URL = "trx_blank_screen_experiment_video_url"

    const val PDF_DEFAULT_FILTER_EXPERIMENT = "pdf_report_filter_variant"

    private const val ENABLE_DAILY_BUSINESS_UPDATE = "enable_daily_business_update"
    private const val ENABLE_DAILY_BUSINESS_UPDATE_FOR_TESTING =
        "enable_daily_business_update_for_testing"
    const val DAILY_BUSINESS_UPDATE_EXPERIMENT = "daily_business_update_experiment"

    private const val TNC_VISIBLE = "tnc_visible"
    private const val TNC_URL = "tnc_url"
    private const val BUKU_SUPPLIER_TITLE = "buku_supplier_title"
    private const val TAB_WITH_TNC = "tab_with_tnc"
    private const val PRIVACY_POLICY_URL = "privacy_policy_url"
    private const val AUTO_RECORD_FAQ_URL = "auto_record_faq_url"
    private const val TNC_TIME_THRESHOLD = "tnc_time_treshold"

    private const val FLOATING_REFERRAL_BUTTON_IMAGE = "floating_referral_button_image"

    private const val UTANG_DUE_DATE_ENABLE = "utang_duedate_enable"

    const val INVENTORY_COGS = "inventory_cogs"

    private const val SHOW_TRANSAKSI_IMAGE = "show_transaksi_image"

    const val CATEGORY_TRANSACTION_CREDIT = "category_transaction_credit"
    const val CATEGORY_TRANSACTION_CREDIT_NEW = "category_transaction_credit_new"
    const val CATEGORY_TRANSACTION_CREDIT_NEW_BACK_UP = "category_transaction_credit_new_back_up"
    const val CATEGORY_TRANSACTION_DEBIT = "category_transaction_debit"
    const val CATEGORY_TRANSACTION_DEBIT_NEW = "category_transaction_debit_new"
    const val CATEGORY_TRANSACTION_DEBIT_NEW_BACK_UP = "category_transaction_debit_new_back_up"

    const val CATEGORY_TRANSACTION_CREDIT_DETAILS = "category_transaction_credit_details"
    const val CATEGORY_TRANSACTION_CREDIT_DETAILS_NEW = "category_transaction_credit_details_new"
    const val CATEGORY_TRANSACTION_DEBIT_DETAILS = "category_transaction_debit_details"
    const val CATEGORY_TRANSACTION_DEBIT_DETAILS_NEW = "category_transaction_debit_details_new"

    const val NEW_CATEGORY_VISIBLE = "new_category_visible"

    private const val ON_BOARDING_CATEGORIES = "on_boarding_categories"
    private const val ON_BOARDING_USAGE_PAST_OPTIONS = "on_boarding_usage_past_options"
    private const val ON_BOARDING_USAGE_GOAL_OPTIONS = "on_boarding_usage_goal_options"

    private const val NEW_HOME_PAGE = "new_home_page"
    private const val NEW_HOME_PAGE_LOCAL = "new_home_page_local"
    private const val SHOW_NEW_HOME_PAGE_EXISTING_USER = "show_new_home_page_existing_user"
    private const val SHOW_NEW_HOME_PAGE_NEW_USER = "show_new_home_page_new_user"
    private const val ENABLE_BRICK_DISCONNECTION = "enable_brick_disconnection"
    private const val PAYMENT_PENDING_TIME_IN_MINUTES = "payment_pending_time_in_minutes"
    private const val HOME_PAGE_JSON_SALDO = "homepage_saldo"
    private const val HOME_PAGE_JSON = "homepage"
    private const val HOME_PAGE_JSON_FAILSAFE = "homepage_local"

    private const val LAINNYA_PROGRESS_BAR = "lainnya_progress_bar"
    private const val BUSINESS_DASHBOARD_CARD = "business_dashboard_card"
    private const val BUSINESS_DASHBOARD_CARD_FAILSAFE = "business_dashboard_card_failsafe"
    private const val APP_UPDATE_VERSION_CODE = "app_update_version_code"

    private const val PRODUCT_CATALOG_ENABLED = "product_catalog_enabled"
    private const val PRODUCT_CATALOG_PAGE_SIZE = "product_catalog_page_size"
    private const val PRODUCT_CATALOG_DATA = "product_catalog_data"
    private const val PRODUCT_CATALOG_ELIGIBLE_BIZZ_TYPE = "product_catalog_eligible_bizz_type"

    private const val SHOW_SET_SELLING_PRICE_NEW = "show_set_selling_price_new"
    private const val PROMOTIONS_BODY = "promotions_body"
    private const val SHOW_MANDATORY_TRANSACTION_CATEGORY = "show_mandatory_transaction_category"
    private const val DEFAULT_PENGELUARAN_STOCK = "default_category_expenses"
    private const val SHOULD_SHOW_AUTO_RECORD_FEATURE = "should_show_auto_record_feature"
    private const val SHOULD_SHOW_TRANSACTION_CARD_FEATURE = "should_show_transactions_card_feature"
    private const val BRICK_LOGIN_URL = "brick_login_url"
    private const val NOTES_MISSION_BANNER_IMAGE = "notes_mission_banner_image"

    private const val LOYALTY_MEMBERSHIP_URL = "loyalty_membership_url"
    private const val LOYALTY_POINT_HISTORY_URL = "loyalty_point_history_url"


    private const val SHOW_HOME_FLOATING_BUTTON = "show_home_floating_button"
    private const val HOME_FLOATING_BUTTON_IMAGE = "home_floating_button_image"
    private const val HOME_FLOATING_BUTTON_REDIRECTION = "home_floating_button_redirection"

    private const val HOME_TOP_BODY = "top_body"
    private const val HOME_PPOB_BODY = "ppob_body"
    private const val HOME_BOOKKEEPING_BODY = "bookkeeping_body"
    private const val HOME_FIXED_BANNER_BODY = "fixed_banner_body"
    private const val HOME_HELP_SECTION_BODY = "help_section_body"
    private const val HOMEPAGE_BANNER_AUTOROTATE = "homepage_banner_autorotate"
    private const val HOMEPAGE_BANNER_DURATION = "homepage_banner_duration"

    private const val HOME_TOP_BODY_NEW = "top_body_new"
    private const val HOME_PPOB_BODY_NEW = "ppob_body_new"
    private const val HOME_BOOKKEEPING_BODY_NEW = "bookkeeping_body_new"
    private const val HOME_LAINNYA_BODY_NEW = "lainnya_body_new"
    private const val HOME_CAROUSEL_BODY_NEW = "carousel_body_new"
    private const val HOME_FIXED_BANNER_BODY_NEW = "fixed_banner_body_new"
    private const val HOME_HELP_SECTION_BODY_NEW = "help_section_body_new"
    private const val INVOICE_DATA_BLOCK = "invoice_data_block"
    private const val INVOICE_DATA_FAILSAFE_BLOCK = "invoice_data_failsafe_block"

    private const val TRANSACTION_CARD_ENABLED = "transaction_card_enabled"
    private const val TRANSACTION_CATEGORY_CARD_ENABLED = "transaction_category_card_enabled"
    private const val CAPITAL_CARD_ENABLED = "capital_card_enabled"
    private const val PRODUCT_CARD_ENABLED = "product_card_enabled"
    private const val UTANG_CARD_ENABLED = "utang_card_enabled"
    private const val PPOB_CARD_ENABLED = "ppob_card_enabled"
    private const val PAYMENT_CARD_ENABLED = "payment_card_enabled"
    private const val ENABLE_NOTA_MISSION = "enable_nota_mission"
    private const val BUSINESS_DASHBOARD_FLOATING_BUTTON_ENABLED =
        "business_card_floating_button_enabled"
    private const val DOWNLOAD_BUSINESS_DASHBOARD = "download_business_dashboard"
    private const val SHOW_NEW_TRANSACTION_CATEGORY = "show_new_transaction_category"
    private const val CATEGORY_UI_VARIANT = "category_ui_variant"
    private const val IS_REFRESH_TOKEN_NEEDED = "is_refresh_token_needed"

    private const val SHOW_TV_INVITED = "show_tv_invited"
    private const val TNC_WEBVIEW_URL = "tnc_webview_url"
    private const val PPOB_BOTTOMSHEET_HOMEPAGE = "ppob_bottomsheet_homepage"
    private const val LAINNYA_BOTTOMSHEET_HOMEPAGE = "lainnya_bottomsheet_homepage"
    private const val PPOB_ERROR_POPUP_CONTENT = "ppob_error_popup_content"
    private const val AERO_ONBOARDING_FORM_ENABLED = "aero_onboarding_form_enabled"
    private const val SHOW_DEFAULT_OTP_SCREEN = "show_default_otp_screen"
    private const val ASSIST_TICKET_PAGE_URL = "assist_ticket_page_url"
    private const val TRANSACTION_CATEGORY_BREAK_UP_COLORS = "transaction_category_break_up_colors"
    private const val APP_TEXTS = "app_texts"
    private const val BELL_BANNER_TAB_NAME = "bell_banner_tab_name"
    private const val SHOW_LUNASKAN_SMS_CHECKBOX = "show_lunaskan_sms_checkout"
    private const val HOME_TOOLARGE_FIX_ENABLED = "home_toolarge_fix_enabled"
    private const val IMAGE_ASSETS_URLS = "image_assets_urls"
    private const val LOAN_URL = "loan_url"

    private const val NEW_UI_TRX_FORM = "new_UI_TRX_form_post_june2022_enabled"
    private const val TOOLARGE_ENABLED = "toolarge_enabled"
    private const val TOOLARGE_THRESHOLD = "toolarge_threshold"
    private const val DATETIMEFORMATTER_EXP = "datetimeformatter_exp"
    private const val RETRY_ON_CONNECTION_FAILURE = "retry_on_connection_failure"
    private const val SHOW_PROMO_TAGS = "show_promo_tags"
    private const val AUTHENTICATOR_REFACTOR = "authenticator_refactor"
    private const val SHOW_NEW_UTANG_INVOICE = "show_new_utang_invoice"
    private const val PROFILE_TAB_FEEDBACK_INFO = "profile_tab_feedback_info"
    private const val SHOW_NEW_POS_INVOICE = "show_new_pos_invoice"
    private const val PAYMENTS_FRAGMENT_DATA = "payments_fragment_data"
    private const val PPOB_PAYMENTS_BOTTOMSHEET = "ppob_payments_bottomsheet"
    private const val PPOB_BODY_PAYMENTS = "ppob_body_payments"
    private const val SHOW_SALDO_BNPL_FRAGMENT = "show_saldo_bnpl_fragment"
    private const val BNPL_URL = "bnpl_url"
    private const val BUKU_MODAL_URL = "buku_modal_url"
    private const val TICKER_FRAGMENT = "ticker_fragment"
    const val KOMISI_AGEN_TERMS_AND_CONDITIONS = "komisi_agen_terms_and_conditions"
    const val KOMISI_AGEN_DASHBOARD = "komisi_agen_dashboard"
    private const val BNPL_ENTRY_AUTOROTATE_DURATION = "bnpl_auto_rotate_entry_point"

    private const val REFRESH_TOKEN_ATTEMPT = "refresh_token_attempt"

    private const val REFERRAL_CONTENTS = "referral_contents"
    private const val CROSS_ADOPTION_POPUP_ENABLED = "cross_adoption_popup_enabled"
    private const val TIME_DIFF_UNIT = "time_diff_unit"
    private const val AUTH_VARIANT = "auth_variant"
    private const val LOYALTY_WIDGET = "loyalty_widget"
    private const val LOYALTY_URL = "loyalty_url"
    private const val REFERRAL_LANDING_URL = "referral_landing_url"
    private const val SHOW_REFERRAL_ENTRY_POINT_HOME = "new_referral_education_material"
    private const val SHOW_INFO_AT_TOP = "show_info_at_top"

    private const val TICKER_HEADER = "ticker_header"
    private const val TICKER_BODY = "ticker_body"

    private const val SHOW_PAYMENT_HEADER = "show_payment_header"

    private const val CUSTOMER_CARE_NUMBER = "customer_care_number"

    private const val HOME_FLOATING_ACTION_BUTTON = "homepage_floating_leaderboard_entry"

    private const val REFERRAL_CONTENT_WEB = "referral_content_web"

    private const val REFERRAL_REMINDER_THRESHOLD = "referral_reminder_threshold"

    private const val ONBOARDING_CAMPAIGN_GAME_TYPE = "onboarding_campaign_game_type"

    private const val ONBOARDING_CAMPAIGN_BLOCK = "onboarding_camapign_block"

    private const val SHOW_ONBOARDING_BOTTOMSHEET = "show_onboarding_bottomsheet"

    private const val ONBOARDING_TYPE = "onboarding_type"

    private const val SHOW_TELEPHONE_HELP = "show_telephone_help"
    private const val SHOW_ZOHODESK_HELP = "show_zohodesk_help"
    private const val APP_MAINTENANCE_DATA = "app_maintenance_data"
    private const val LEARN_ACCOUNT_LEVELS = "learn_account_levels"
    private const val REFEREE_POINT_ENTRY_DATA = "entry_point_referee_data"
    private const val SUBSCRIPTION_ENTRY_POINT = "subscription_entry_point"

    private val DEFAULTS: HashMap<String, Any> =
        hashMapOf(
            VALUE_N to 10,
            VALUE_M to 12,
            SHOW_LUNASKAN_SMS_CHECKBOX to false,
            BELL_BANNER_TAB_NAME to "OTHERS",
            BNPL_URL to RemoteConfigConst.BNPL_URL,
            BUKU_MODAL_URL to RemoteConfigConst.BUKU_MODAL_URL,
            OTP_ASSIST_URL to "https://bukuwarungsupport.zendesk.com/hc/id-id",
            REFERRAL_ELIGIBLE_LEVEL to -1,
            GAMIFY_DIALOG_STATUS to 1,
            INVOICE_VISIBILITY_THRESHOLD to 0,
            ALLOWED_INVOICE_IGNORE_TRANSACTION to 3,
            FORCED_INVOICE_VISIBILITY_THRESHOLD to 10,
            FORCED_INVOICE_UTANG_VISIBILITY_THRESHOLD to 3,
            INVOICE_POWER_USER_TRANSACTION to 3,
            VALUE_PROFILE_VISIT_CONFIG_COUNT to 10,
            REFERRAL_IMAGE_URL to "",
            REFERRAL_WA_MESSAGE to RemoteConfigConst.REFERRAL_WA_MESSAGE,
            SALES_ONBOARDING_THRESHOLD to 1,
            UNPAID_TRANSACTION_SUPPORT to true,
            VALUE_PROFILE_VISIT_CONFIG_COUNT to 10,
            SHOW_PROFILE_SETUP_DIALOG to true,
            SHOW_DISBURSEMENT_CONFIRMATION_DIALOG to false,
            FORCE_RELOAD_WEBVIEW to false,
            WELCOME_SCREENS to RemoteConfigConst.WELCOME_SCREENS,
            WELCOME_SCREEN_SLIDE_DURATION to 2000,
            SHOW_UTANG_SUCCESS_ANIMATION to false,
            SEND_SMS_UTANG to false,
            SEND_SMS_TRANSACTION to false,
            BUSINESS_CARD_LOCKED_DESIGN to RemoteConfigConst.BUSINESS_CARD_LOCKED_DESIGN,
            SHOW_INCENTIVE_LAYOUT to true,
            SHOW_LOYALTY_LAYOUT to true,
            SHOW_SALDO_BONUS to true,
            SHOW_CASH_SUCCESS_ANIMATION to false,
            TRX_SUCCESS_MESSAGE to "Transaksi berhasil dicatat",
            ASSIST_URL to "https://bukuwarung1.zohodesk.com/portal/id/home",
            LANDING_DIALOG_HEADER to "Makin Sering Tagih dan Bayar, BISA BANJIR CUAN!",
            LANDING_DIALOG_BODY to "Tagih dan bayar sebanyak-banyaknya dan dapatkan total hadiah 15 juta rupiah.",
            LANDING_DIALOG_BUTTON_TEXT to "Yuk Ikutan!",
            STREAK_VARIANT to 1,
            UTANG_ENTRY_OLD to false,
            SHOW_EXIT_DIALOG to true,
            BULK_TRANSAKSI_THRESHOLD to 5,
            IS_BULK_TRANSAKSI_ENABLED to true,
            ON_BOARDING_FULL_QUESTION to true,
            SHOW_DEFAULT_OTP_SCREEN to false,
            OTP_CHANNEL to NotificationChannel.SMS.value,
            IS_PAYMENT_CATEGORY_MANDATORY to true,
            PPOB_ERROR_POPUP_CONTENT to RemoteConfigConst.PPOB_ERROR_POPUP_CONTENT_CONFIG,
            SHOW_RECENTS_AND_FAVOURITES to true,
            PAYMENT_BANNER_URL to "https://bw-banners.s3-ap-southeast-1.amazonaws.com/ic_payment_banner.webp",
            HIDE_CATEGORY to false,
            HIDE_NOTES to false,
            HIDE_INVENTORY to false,
            SHOW_HELP_ICON to true,
            REDIRECT_HELP_ICON to RemoteConfigConst.REDIRECT_HELP_ICON_CUSTOMER_SUPPORT,
            SHOW_APP_SHORTCUTS to true,
            PROFILE_PINS to RemoteConfigConst.PROFILE_PIN,
            USER_PROFILE_OPTIONS to RemoteConfigConst.USER_PROFILE_MENU,
            NOTES_MISSION_STEPS to RemoteConfigConst.NOTES_MISSION_STEPS,
            TRANSACTION_CATEGORY_BREAK_UP_COLORS to RemoteConfigConst.TRANSACTION_CATEGORY_BREAK_UP_COLORS,
            PROFILE_PINS_COLUMNS to 3,
            SHOW_ALL_WHITE to true,
            HIDE_CUSTOMER_NAME to false,
            ASSIST_URL_APPEND_BOOK to true,
            USER_COUNT_ON_WELCOME_SCREEN to "6.500.000",
            STREAK_VARIANT to 1,
            TRX_SUCCESS_MESSAGE to "Transaksi berhasil dicatat",
            STREAK_VARIANT to 1,
            REFERRAL_URL to "https://bukuwarung-referral-staging.web.app/#/",
            DAILY_UPDATE_URL to "https://bw-onboarding-web.web.app/story",
            BUSINESS_PROFILE_URL to "https://bw-onboarding-web.web.app/business-form",
            DAILY_UPDATE_MINIMUM_TRANSACTION_NUMBER to "5",
            DAILY_UPDATE_ACTIVATION_HOUR to "14",
            DAILY_UPDATE_ACTIVATION_MINUTE to "20",
            STICKER_REWARD_DEEPLINK to "https://bwrg.page.link/?type=act&data=com.bukuwarung.activities.home.MainActivity&sticker_streak",
            SHOW_POS_ONBOARDING to false,
            POS_NON_CASH_ENABLE to false,
            POS_PAYMENT_WALLETS to RemoteConfigConst.POS_PAYMENT_WALLETS,
            FAVORITE_CUSTOMER_ENABLED to false,
            PINNED_FAVORITE_CUSTOMERS_LIMIT to 6,
            LIST_FAVORITE_CUSTOMERS_LIMIT to 3,
            RECENT_CUSTOMERS_LIMIT to 3,
            ENABLE_HAMBURGER_MENU_BANNER to true,
            HAMBURGER_MENU_BANNER_URL to "https://bw-banners.s3-ap-southeast-1.amazonaws.com/ic_payment_banner.webp",
            HAMBURGER_MENU_LANDING to "",
            ADD_FAVOURITE_DIALOG to ADD_FAVOURITE_DIALOG_CONFIG,
            LIGHTNING_FAST_SALDO_TEXT to "Yey! Pembayaran jadi 3x lebih cepat pakai Saldo⚡",
            USE_LIGHTNING_FAST_SALDO to "Bayar pakai saldo, transaksi lebih cepat⚡",
            PROFILE_TAB_NORMAL_BANNER_URL_NEW to
                    "[" +
                    // show empty banner if we got nothing from remote config
//                            "  {" +
//                            "    \"bannerUrl\": \"https://bw-banners.s3-ap-southeast-1.amazonaws.com/ic_payment_banner.webp\"," +
//                            "    \"redirectionUrl\": \"https://bukuwarung.com/bantuan\"" +
//                            "    \"type\": \"leaderboard\"" +
//                            "    \"appVersion\": \"3.16.0"" +
//                            "  }" +
                    "]",
            PROFILE_TAB_LENDING_BANNER_URL_NEW to
                    "[" +
                    // show empty banner if we got nothing from remote config
//                            "  {" +
//                            "    \"bannerUrl\": \"https://bw-assets.s3.ap-southeast-1.amazonaws.com/campaign/lending-banner.webp\"," +
//                            "    \"redirectionUrl\": \"https://bukuwarung.com\"" +
//                            "    \"type\": \"leaderboard\"" +
//                            "    \"appVersion\": \"3.16.0"" +
//                            "  }" +
                    "]",
            HAMBURGER_MENU_LANDING to "",
            NEW_BUSINESS_CARD_DESIGN to RemoteConfigConst.NEW_BUSINESS_CARD_DESIGNS,
            BUSINESS_CARD_LOCKED_DESIGN to RemoteConfigConst.BUSINESS_CARD_LOCKED_DESIGN,
            NEW_BUSINESS_CARD_ENABLED to true,
            POSTER_REWARD_DEEPLINK to "https://bwrg.page.link/?type=act&data=com.bukuwarung.activities.home.MainActivity&social_media_poster",
            STATUS_REWARD_DEEPLINK to "https://bwrg.page.link/?type=act&data=com.bukuwarung.activities.home.MainActivity&social_media_status",
            SOCIAL_MEDIA_POSTER_DATA to RemoteConfigConst.SOCIAL_MEDIA_POSTERS,
            SOCIAL_MEDIA_STATUS_DATA to RemoteConfigConst.SOCIAL_MEDIA_STATUS,
            REFERRAL_CONTACT_LIST_ENABLED to true,
            STOCK_ON_OFF_TOGGLE to true,
            REFERRAL_FLOATING_BUTTON to true,
            REFERRAL_FLOATING_BUTTON_VISIBILITY_THRESHOLD to 10,
            REFERRAL_FLOATING_BUTTON_REDIRECTION to "",
            TRX_BLANK_SCREEN_VARIANT to TRX_BLANK_SCREEN_VARIANT_SHOW_COACHMARK,
            TRX_BLANK_SCREEN_EXPERIMENT_VIDEO_URL to "https://youtu.be/cqSFYBHyEgw",
            DAILY_BUSINESS_UPDATE_EXPERIMENT to false,
            ENABLE_DAILY_BUSINESS_UPDATE to false,
            ENABLE_DAILY_BUSINESS_UPDATE_FOR_TESTING to false,
            MINIMUM_PAYMENT_AMOUNT to 10000.0,
            SALDO_MIN_TOPUP_AMOUNT to SALDO_MIN_TOPUP_AMOUNT_VALUE,
            MINIMUM_PAYMENT_OUT_AMOUNT to 10000.0,
            BUSINESS_PROFILE_VARIANT to RemoteConfigConst.BUSINESS_PROFILE_VARIANT_NEW_NATIVE,
            FLOATING_REFERRAL_BUTTON_IMAGE to "https://i.ibb.co/s585VM0/Group.png",
            PDF_DEFAULT_FILTER_EXPERIMENT to 0,
            SHOW_NEW_LOGIN_SCREEN to true,
            TNC_VISIBLE to true,
            TNC_URL to "https://bukuwarung.com/tentang/",
            BUKU_SUPPLIER_TITLE to "Buku Supplier",
            PRIVACY_POLICY_URL to "https://bukuwarung.com/",
            AUTO_RECORD_FAQ_URL to "https://bukuwarungsupport.zendesk.com/hc/id-id/categories/4413577280015--Fitur-Catat-Otomatis",
            TAB_WITH_TNC to RemoteConfigConst.TNC_TAB_CONFIG,
            KYC_LAYOUT_MIN_THRESHOLD to 1,
            TNC_TIME_THRESHOLD to 0,
            FLOATING_REFERRAL_BUTTON_IMAGE to "https://i.ibb.co/s585VM0/Group.png",
            UTANG_DUE_DATE_ENABLE to false,
            ENABLE_BRICK_DISCONNECTION to false,
            PAYMENT_PENDING_TIME_IN_MINUTES to 30,
            NEW_ON_BOARDING_FLOW_FLAG to true,
            ON_BOARDING_VARIANT to 1,
            ON_BOARDING_FORMS to """"business_name","business_category", "usage_goal"""",
            USER_ONBOARDING_FORMS to "business_name,business_category,usage_goal",
            ON_BOARDING_REDIRECTION to "default",
            ON_BOARDING_URL to "https://bw-onboarding.web.app/onboarding_loading",
            SHOW_TRANSAKSI_IMAGE to true,
            INVENTORY_COGS to false,
            CATEGORY_TRANSACTION_CREDIT to RemoteConfigConst.CATEGORY_CREDIT,
            CATEGORY_TRANSACTION_CREDIT_NEW to RemoteConfigConst.CATEGORY_CREDIT_NEW,
            CATEGORY_TRANSACTION_CREDIT_NEW_BACK_UP to RemoteConfigConst.CATEGORY_CREDIT_NEW_BACK_UP,
            CATEGORY_TRANSACTION_DEBIT to RemoteConfigConst.CATEGORY_DEBIT,
            CATEGORY_TRANSACTION_DEBIT_NEW to RemoteConfigConst.CATEGORY_DEBIT_NEW,
            CATEGORY_TRANSACTION_DEBIT_NEW_BACK_UP to RemoteConfigConst.CATEGORY_DEBIT_NEW_BACK_UP,
            CATEGORY_TRANSACTION_CREDIT_DETAILS to RemoteConfigConst.CATEGORY_INFO_CREDIT,
            CATEGORY_TRANSACTION_CREDIT_DETAILS_NEW to RemoteConfigConst.CATEGORY_INFO_CREDIT_NEW,
            CATEGORY_TRANSACTION_DEBIT_DETAILS to RemoteConfigConst.CATEGORY_INFO_DEBIT,
            CATEGORY_TRANSACTION_DEBIT_DETAILS_NEW to RemoteConfigConst.CATEGORY_INFO_DEBIT_NEW,
            NEW_CATEGORY_VISIBLE to true,
            ON_BOARDING_CATEGORIES to RemoteConfigConst.ON_BOARDING_CATEGORIES,
            ON_BOARDING_USAGE_GOAL_OPTIONS to RemoteConfigConst.ON_BOARDING_USAGE_GOAL_OPTIONS,
            ON_BOARDING_USAGE_PAST_OPTIONS to RemoteConfigConst.ON_BOARDING_USAGE_PAST_OPTIONS,
            NEW_HOME_PAGE to RemoteConfigConst.NEW_HOME_PAGE_JSON,
            NEW_HOME_PAGE_LOCAL to RemoteConfigConst.NEW_HOME_PAGE_JSON,
            SHOW_NEW_HOME_PAGE_EXISTING_USER to true,
            SHOW_NEW_HOME_PAGE_NEW_USER to true,
            PRODUCT_CATALOG_ENABLED to false,
            PRODUCT_CATALOG_PAGE_SIZE to 20,
            PRODUCT_CATALOG_DATA to RemoteConfigConst.PRODUCT_CATALOG_DATA,
            SHOW_MANDATORY_TRANSACTION_CATEGORY to true,
            PRODUCT_CATALOG_ELIGIBLE_BIZZ_TYPE to "",
            SHOW_SET_SELLING_PRICE_NEW to false,

            PROMOTIONS_BODY to RemoteConfigConst.PROMOTIONS_BODY,
            DEFAULT_PENGELUARAN_STOCK to false,

            QRIS_RETRY_CONFIGS to RemoteConfigConst.QRIS_RETRY_CONFIG_DATA,
            PAYMENT_CONFIGS to RemoteConfigConst.PAYMENT_CONFIGS_DATA,
            MX_MWEB_CONFIGS to RemoteConfigConst.MX_MWEB_CONFIG_DATA,
            PPOB_CONFIG to RemoteConfigConst.PPOB_CONFIG,
            EDC_CONFIG to RemoteConfigConst.EDC_CONFIG,
            APP_TEXTS to RemoteConfigConst.APP_TEXTS,
            APPLY_NEW_COMPRESSION to false,
            PERFORM_VERSION_CHECK to false,
            RETRY_ON_CONNECTION_FAILURE to true,
            MIN_SUPPORTED_VERSIONS to hashMapOf<String, String>(),
            PHYSICAL_VISIT_CITIES to "",
            SHOW_HOME_FLOATING_BUTTON to false,
            HOME_FLOATING_BUTTON_IMAGE to "https://i.ibb.co/VxWrY3X/FLOATING-ICON2-01-1-1.png",
            HOME_FLOATING_BUTTON_REDIRECTION to "https://bw-webview-dev.web.app/yearend-story-loading",

            SHOW_ASSIST_PAGE to true,
            SHOW_OLD_PAYMENT_OUT_UI to false,
            HOME_PAGE_JSON to RemoteConfigConst.HOMEPAGE_JSON,
            HOME_PAGE_JSON_FAILSAFE to RemoteConfigConst.HOMEPAGE_JSON,
            HOME_PAGE_JSON_SALDO to RemoteConfigConst.HOMEPAGE_JSON_SALDO,
            HOME_TOP_BODY to RemoteConfigConst.TOP_BLOCK_BODY,
            HOME_PPOB_BODY to RemoteConfigConst.PPOB_BLOCK_BODY,
            HOME_BOOKKEEPING_BODY to RemoteConfigConst.BOOKKEEPING_BLOCK_BODY,
            HOME_FIXED_BANNER_BODY to RemoteConfigConst.FIXED_BANNER_BLOCK_BODY,
            HOME_HELP_SECTION_BODY to RemoteConfigConst.HELP_BLOCK_BODY,
            HOMEPAGE_BANNER_AUTOROTATE to false,
            HOMEPAGE_BANNER_DURATION to 2000,
            HOME_TOP_BODY_NEW to RemoteConfigConst.TOP_BLOCK_BODY_NEW,
            HOME_PPOB_BODY_NEW to RemoteConfigConst.PPOB_BLOCK_BODY_NEW,
            HOME_BOOKKEEPING_BODY_NEW to RemoteConfigConst.BOOKKEEPING_BLOCK_BODY_NEW,
            HOME_LAINNYA_BODY_NEW to RemoteConfigConst.LAINNYA_BLOCK_BODY_NEW,
            HOME_CAROUSEL_BODY_NEW to RemoteConfigConst.CAROUSEL_BLOCK_BODY_NEW,
            HOME_FIXED_BANNER_BODY_NEW to RemoteConfigConst.FIXED_BANNER_BLOCK_BODY_NEW,
            HOME_HELP_SECTION_BODY_NEW to RemoteConfigConst.HELP_BLOCK_BODY_NEW,
            INVOICE_DATA_BLOCK to RemoteConfigConst.INVOICE_DATA_BLOCK_JSON,
            INVOICE_DATA_FAILSAFE_BLOCK to RemoteConfigConst.INVOICE_DATA_BLOCK_JSON,
            SHOULD_SHOW_AUTO_RECORD_FEATURE to false,
            SHOULD_SHOW_TRANSACTION_CARD_FEATURE to false,
            BRICK_LOGIN_URL to "https://buku-poc.web.app/brick-loading",
            NOTES_MISSION_BANNER_IMAGE to RemoteConfigConst.NOTES_MISSION_BANNER_IMAGE,
            LOYALTY_MEMBERSHIP_URL to "https://release-3-25-0-loyalty.d2fmr1gjayhao2.amplifyapp.com/membership-loading",
            LOYALTY_POINT_HISTORY_URL to "https://release-3-25-0-loyalty.d2fmr1gjayhao2.amplifyapp.com/buku-poin",
            LAINNYA_PROGRESS_BAR to RemoteConfigConst.LAINNYA_PROGRESS_BAR_JSON,
            TRANSACTION_CARD_ENABLED to true,
            TRANSACTION_CATEGORY_CARD_ENABLED to true,
            CAPITAL_CARD_ENABLED to true,
            PRODUCT_CARD_ENABLED to true,
            UTANG_CARD_ENABLED to true,
            BUSINESS_DASHBOARD_FLOATING_BUTTON_ENABLED to true,
            LOYALTY_POINT_HISTORY_URL to "https://release-3-25-0-loyalty.d2fmr1gjayhao2.amplifyapp.com/buku-poin",
            LAINNYA_PROGRESS_BAR to RemoteConfigConst.LAINNYA_PROGRESS_BAR_JSON,
            ENABLE_NOTA_MISSION to false,
            BUSINESS_DASHBOARD_CARD to RemoteConfigConst.BUSINESS_DASHBOARD_JSON,
            BUSINESS_DASHBOARD_CARD_FAILSAFE to RemoteConfigConst.BUSINESS_DASHBOARD_JSON,
            APP_UPDATE_VERSION_CODE to RemoteConfigConst.APP_UPDATE_VERSION_CODE_JSON,
            DOWNLOAD_BUSINESS_DASHBOARD to true,
            SHOW_NEW_TRANSACTION_CATEGORY to 2,
            CATEGORY_UI_VARIANT to 1,
            PPOB_CARD_ENABLED to false,
            IS_REFRESH_TOKEN_NEEDED to false,
            NEW_BUSINESS_CARD_DESIGN to RemoteConfigConst.NEW_BUSINESS_CARD_DESIGNS,
            PPOB_BOTTOMSHEET_HOMEPAGE to RemoteConfigConst.PPOB_BOTTOMSHEET_HOMEPAGE,
            LAINNYA_BOTTOMSHEET_HOMEPAGE to RemoteConfigConst.LAINNYA_BOTTOMSHEET_HOMEPAGE,
            AERO_ONBOARDING_FORM_ENABLED to true,
            NEW_BUSINESS_CARD_DESIGN to RemoteConfigConst.NEW_BUSINESS_CARD_DESIGNS,
            SHOW_DEFAULT_OTP_SCREEN to false,
            ASSIST_TICKET_PAGE_URL to BuildConfig.ASSIST_TICKET_PAGE_URL,
            HOME_TOOLARGE_FIX_ENABLED to false,
            ASSIST_TICKET_PAGE_URL to BuildConfig.ASSIST_TICKET_PAGE_URL,
            IMAGE_ASSETS_URLS to RemoteConfigConst.IMAGE_ASSETS_URLS_JSON,
            LOAN_URL to "https://api-dev.bukuwarung.com/los-web/bnpl",
            NEW_UI_TRX_FORM to false,
            TOOLARGE_ENABLED to false,
            TOOLARGE_THRESHOLD to 0.0,
            DATETIMEFORMATTER_EXP to false,
            SHOW_PROMO_TAGS to true,
            SHOW_NEW_UTANG_INVOICE to true,
            PROFILE_TAB_FEEDBACK_INFO to RemoteConfigConst.PROFILE_TAB_FEEDBACK_INFO,
            SHOW_SALDO_BNPL_FRAGMENT to true,
            SHOW_NEW_POS_INVOICE to true,
            AUTHENTICATOR_REFACTOR to false,
            REFRESH_TOKEN_ATTEMPT to 3,
            REFERRAL_CONTENTS to RemoteConfigConst.REFERRAL_CONTENT_DATA,
            TICKER_FRAGMENT to TICKER_FRAGMENT_DATA,
            BNPL_ENTRY_AUTOROTATE_DURATION to 3000,
            PAYMENTS_FRAGMENT_DATA to RemoteConfigConst.PAYMENTS_FRAGMENT_DATA,
            PPOB_PAYMENTS_BOTTOMSHEET to RemoteConfigConst.PPOB_PAYMENTS_BOTTOMSHEET,
            PPOB_BODY_PAYMENTS to RemoteConfigConst.PPOB_BODY_PAYMENTS,
            CROSS_ADOPTION_POPUP_ENABLED to true,
            TIME_DIFF_UNIT to 0,
            AUTH_VARIANT to 0,
            LOYALTY_WIDGET to 0,
            LOYALTY_URL to "https://staging.d22bg8i31pway2.amplifyapp.com/loyalty-homepage",
            SHOW_INFO_AT_TOP to false,
            PAYMENTS_FRAGMENT_DATA to RemoteConfigConst.PAYMENTS_FRAGMENT_DATA,
            PPOB_PAYMENTS_BOTTOMSHEET to RemoteConfigConst.PPOB_PAYMENTS_BOTTOMSHEET,
            PPOB_BODY_PAYMENTS to RemoteConfigConst.PPOB_BODY_PAYMENTS,
            TICKER_HEADER to "Info Seputar Pembayaran",
            TICKER_BODY to "Pembayaran dengan bank BCA akan mengalami keterlambatan sampai pukula. <font color='#0091FF'>Baca selengkapnya </font>",
            SHOW_PAYMENT_HEADER to true,
            CUSTOMER_CARE_NUMBER to "********",
            HOME_FLOATING_ACTION_BUTTON to RemoteConfigConst.HOME_FAB_CONFIG_JSON,
            REFERRAL_CONTENT_WEB to RemoteConfigConst.REFERRAL_CONTENT_WEB,
            REFERRAL_REMINDER_THRESHOLD to 7,
            ONBOARDING_CAMPAIGN_BLOCK to RemoteConfigConst.ONBOARDING_CAMPAIGN_BLOCK,
            ONBOARDING_CAMPAIGN_GAME_TYPE to RemoteConfigConst.ONBOARDING_CAMPAIGN_GAME_TYPE,
            SHOW_ONBOARDING_BOTTOMSHEET to false,
            SHOW_TELEPHONE_HELP to false,
            SHOW_ZOHODESK_HELP to false,
            APP_MAINTENANCE_DATA to RemoteConfigConst.APP_MAINTENANCE_DATA,
            ONBOARDING_TYPE to 0,
            LEARN_ACCOUNT_LEVELS to "https://bukuwarung.com/verifikasi-data/",
            SHOW_REFERRAL_ENTRY_POINT_HOME to false,
            REFEREE_POINT_ENTRY_DATA to RemoteConfigConst.REFEREE_ENTRY_POINT_DATA
        )

    val remoteConfig: FirebaseRemoteConfig by lazy {
        getFirebaseRemoteConfig()
    }

    fun init() {
        remoteConfig.toString()
    }

    fun getFirebaseRemoteConfig(): FirebaseRemoteConfig {

        val remoteConfig = Firebase.remoteConfig

        val configSettings = remoteConfigSettings {
            minimumFetchIntervalInSeconds = if (BuildConfig.DEBUG) {
                0 // Kept 0 for quick debug
            } else {
                60 * 60
            }
            fetchTimeoutInSeconds = 5000
        }

        remoteConfig.setConfigSettingsAsync(configSettings)
        remoteConfig.setDefaultsAsync(DEFAULTS)
        remoteConfig.fetchAndActivate().addOnCompleteListener {
            Log.d(TAG, "addOnCompleteListener")
            if (it.isSuccessful) {
                remoteConfig.fetchAndActivate()
            }
        }

        return remoteConfig
    }

    fun getValueOfN(): Long = remoteConfig.getLong(VALUE_N)

    fun getSalesOnboardingThreshold(): Long {
        val txnCountThreshold = remoteConfig.getLong(SALES_ONBOARDING_THRESHOLD)
        return txnCountThreshold
    }

    fun doWebviewForceReload(): Boolean {
        return remoteConfig.getBoolean(FORCE_RELOAD_WEBVIEW)
    }

    fun showOnboardingBottomsheet(): Boolean {
        return remoteConfig.getBoolean(SHOW_ONBOARDING_BOTTOMSHEET)
    }

    fun showShowLunaskanSMS(): Boolean {
        return remoteConfig.getBoolean(SHOW_LUNASKAN_SMS_CHECKBOX)
    }

    fun getEnableBrickDisconnection(): Boolean {
        return remoteConfig.getBoolean(ENABLE_BRICK_DISCONNECTION)
    }

    fun getPaymentPendingTimeInMinutes(): Int {
        return remoteConfig.getLong(PAYMENT_PENDING_TIME_IN_MINUTES).toInt()
    }

    fun getPpobErrorPopupContent(): String {
        return getRemoteConfigString(PPOB_ERROR_POPUP_CONTENT)
    }

    fun getGamifyDialogStatus(): Long {
        val gamifyDialogStatus = remoteConfig.getLong(GAMIFY_DIALOG_STATUS)
        return gamifyDialogStatus
    }

    fun getInvoiceVisibilityThreshold(): Long {
        val invoiceVisibilityThreshold = remoteConfig.getLong(INVOICE_VISIBILITY_THRESHOLD)
        return invoiceVisibilityThreshold
    }

    fun getAllowedIgnoreRecieptCount(): Long {
        val allowedIgnoreRecieptCount = remoteConfig.getLong(ALLOWED_INVOICE_IGNORE_TRANSACTION)
        return allowedIgnoreRecieptCount
    }

    fun getForcedInvoiceVisibilityThreshold(): Long {
        val forcedInvoiceVisibilityThreshold = remoteConfig.getLong(FORCED_INVOICE_VISIBILITY_THRESHOLD)
        return forcedInvoiceVisibilityThreshold
    }

    fun getForcedUtangInvoiceVisibilityThreshold(): Long {
        val forcedUtangInvoiceVisibilityThreshold = remoteConfig.getLong(
            FORCED_INVOICE_UTANG_VISIBILITY_THRESHOLD
        )
        return forcedUtangInvoiceVisibilityThreshold
    }

    fun getBellBannerTab(): String = getRemoteConfigString(BELL_BANNER_TAB_NAME)

    fun getInvoicePowerUserTxnCriteria(): Long {
        val invoicePowerUserTxnCriteria = remoteConfig.getLong(INVOICE_POWER_USER_TRANSACTION)
        return invoicePowerUserTxnCriteria
    }

    fun getValueOfM(): Long = remoteConfig.getLong(VALUE_M)

    fun isUpaidTransactionSupported(): Boolean {
        val unpaidTransactionEnabled = remoteConfig.getBoolean(UNPAID_TRANSACTION_SUPPORT)
        return remoteConfig.getBoolean(UNPAID_TRANSACTION_SUPPORT)
    }

    fun shouldShowProfileSetupDialog(): Boolean = remoteConfig.getBoolean(SHOW_PROFILE_SETUP_DIALOG)

    fun shouldShowDisbursementConfirmationDialog(): Boolean =
        remoteConfig.getBoolean(SHOW_DISBURSEMENT_CONFIRMATION_DIALOG)

    fun getWelcomeScreens(): String = getRemoteConfigString(WELCOME_SCREENS)

    fun getWelcomeScreenSlideDuration(): Long = remoteConfig.getLong(WELCOME_SCREEN_SLIDE_DURATION)

    fun shouldShowUtangSuccessAnimation(): Boolean = remoteConfig.getBoolean(SHOW_UTANG_SUCCESS_ANIMATION)

    fun shouldShowIncentiveLayout(): Boolean = remoteConfig.getBoolean(SHOW_INCENTIVE_LAYOUT)

    fun shouldShowLoyaltyLayout(): Boolean = remoteConfig.getBoolean(SHOW_LOYALTY_LAYOUT)

    fun shouldShowSaldoBonus(): Boolean = remoteConfig.getBoolean(SHOW_SALDO_BONUS)

    fun shouldShowCashSuccessAnimation(): Boolean = remoteConfig.getBoolean(SHOW_CASH_SUCCESS_ANIMATION)

    fun getTrxSuccessMessage(): String = getRemoteConfigString(TRX_SUCCESS_MESSAGE)

    fun getAssistUrl(): String = getRemoteConfigString(ASSIST_URL)
    fun getOtpAssistUrl(): String = getRemoteConfigString(OTP_ASSIST_URL)
    fun getBnplUrl(): String = getRemoteConfigString(BNPL_URL)
    fun getBukuModalUrl(): String = getRemoteConfigString(BUKU_MODAL_URL)
    fun shouldAssistUrlAppendBook(): Boolean = remoteConfig.getBoolean(ASSIST_URL_APPEND_BOOK)

    fun fetchProfileTabFeedbackInfo(): ProfileTabFeedbackInfo? {
        val profileTabFeedbackInfoJson = remoteConfig.getString(PROFILE_TAB_FEEDBACK_INFO)
        val type = object : TypeToken<ProfileTabFeedbackInfo?>() {}.type
        return type.returnObject(profileTabFeedbackInfoJson)
    }

    fun shouldSendSmsUtang(): Boolean = remoteConfig.getBoolean(SEND_SMS_UTANG)

    fun shouldSendSmsTransaction(): Boolean = remoteConfig.getBoolean(SEND_SMS_TRANSACTION)

    fun getBusinessProfileVariant(): Int {
        val variantId = remoteConfig.getLong(BUSINESS_PROFILE_VARIANT).toInt()
        return variantId
    }

    fun shouldShowOldUtangForm(): Boolean = remoteConfig.getBoolean(UTANG_ENTRY_OLD)

    fun shouldShowExitDialog(): Boolean = remoteConfig.getBoolean(SHOW_EXIT_DIALOG)

    fun getBulkTransaksiThreshold(): Long = remoteConfig.getLong(BULK_TRANSAKSI_THRESHOLD)

    fun shouldShowBulkTransaksi(): Boolean = remoteConfig.getBoolean(IS_BULK_TRANSAKSI_ENABLED)


    object OnBoarding {
        // for old design onboarding questions
        fun shouldShowFullOnBoardingQuestion(): Boolean = remoteConfig.getBoolean(ON_BOARDING_FULL_QUESTION)

        // 0 for old onBoarding
        // 1 for new onBoarding
        fun getOnBoardingVariant(): Int = remoteConfig.getLong(ON_BOARDING_VARIANT).toInt()

        fun getOnBoardingForms(): String = getRemoteConfigString(ON_BOARDING_FORMS)
        fun getOnBoardingFormsAsList(): List<String> {
            return try {
                val json = getRemoteConfigString(USER_ONBOARDING_FORMS)
                json.split(",").map { it -> it.trim() }
            } catch (ex: Exception) {
                emptyList()
            }
        }

        fun getOnBoardingUrl(): String = getRemoteConfigString(ON_BOARDING_URL)
        fun getRedirectionRule(): OnBoardingRedirection {
            return try {
                // based on user choice on OnBoarding Form
                OnBoardingRedirection.valueOf(
                    getRemoteConfigString(ON_BOARDING_REDIRECTION).toUpperCase(
                        Locale.getDefault()
                    )
                )
            } catch (ex: Exception) {
                // default to Transaksi Tab
                OnBoardingRedirection.DEFAULT
            }
        }

        private val type = object : TypeToken<List<CategoryOption>>() {}.type

        fun getBusinessCategories(): List<CategoryOption> {
            return try {
                val json = getRemoteConfigString(ON_BOARDING_CATEGORIES)
                Gson().fromJson(json, type)
            } catch (ex: Exception) {
                ex.recordException()
                emptyList()
            }
        }

        fun getUsageGoalOptions(): List<CategoryOption> {
            return try {
                val json = getRemoteConfigString(ON_BOARDING_USAGE_GOAL_OPTIONS)
                Gson().fromJson(json, type)
            } catch (ex: Exception) {
                ex.recordException()
                emptyList()
            }
        }

        fun getUsagePastOptions(): List<CategoryOption> {
            return try {
                val json = getRemoteConfigString(ON_BOARDING_USAGE_PAST_OPTIONS)
                Gson().fromJson(json, type)
            } catch (ex: Exception) {
                ex.recordException()
                emptyList()
            }
        }

    }

    fun getTransactionCategoryBreakUpColors(): List<TransactionCategoryBreakUpColor> {
        val type = object : TypeToken<List<TransactionCategoryBreakUpColor>>() {}.type

        return try {
            val json = getRemoteConfigString(TRANSACTION_CATEGORY_BREAK_UP_COLORS)
            Gson().fromJson(json, type)
        } catch (ex: Exception) {
            ex.recordException()
            emptyList()
        }
    }

    fun shouldShowDefaultOtpScreen(): Boolean = remoteConfig.getBoolean(SHOW_DEFAULT_OTP_SCREEN)

    fun showAssistPage(): Boolean = remoteConfig.getBoolean(SHOW_ASSIST_PAGE)

    fun showOldPaymentOutUi(): Boolean = remoteConfig.getBoolean(SHOW_OLD_PAYMENT_OUT_UI)

    fun getOtpChannel(): String = NotificationChannel.SMS.value

    fun shouldShowHelpIcon(): Boolean = remoteConfig.getBoolean(SHOW_HELP_ICON)

    fun redirectHelpIcon(): String = getRemoteConfigString(REDIRECT_HELP_ICON)

    fun shouldShowAppShortcuts(): Boolean = remoteConfig.getBoolean(SHOW_APP_SHORTCUTS)

    fun shouldShowNewLoginScreen(): Boolean = remoteConfig.getBoolean(SHOW_NEW_LOGIN_SCREEN)

    fun getPaymentBannerUrl(): String = getRemoteConfigString(PAYMENT_BANNER_URL)

    fun isPaymentCategoryMandatory(): Boolean = remoteConfig.getBoolean(IS_PAYMENT_CATEGORY_MANDATORY)

    fun showRecentsAndFavourites(): Boolean = remoteConfig.getBoolean(SHOW_RECENTS_AND_FAVOURITES)

    fun hideCategory(): Boolean = remoteConfig.getBoolean(HIDE_CATEGORY)

    fun getProfilePins(): String = getRemoteConfigString(PROFILE_PINS)

    fun getUserProfileOptions(): String = getRemoteConfigString(USER_PROFILE_OPTIONS)

    fun getNotesMissionSteps(): String = getRemoteConfigString(NOTES_MISSION_STEPS)

    fun getProfilePinColumnCount(): Long = remoteConfig.getLong(PROFILE_PINS_COLUMNS)

    fun showPreviousTransaction(): Boolean = remoteConfig.getBoolean(SHOW_PREVIOUS_TRANSAKSI)

    fun showAllFieldsWithWhite(): Boolean = remoteConfig.getBoolean(SHOW_ALL_WHITE)

    fun hideContact(): Boolean = remoteConfig.getBoolean(HIDE_CUSTOMER_NAME)

    fun getUserCountForWelcomeScreen(): String = getRemoteConfigString(USER_COUNT_ON_WELCOME_SCREEN)

    fun getReferralUrl(): String = getRemoteConfigString(REFERRAL_URL)

    fun getDailyUpdateUrl(): String = getRemoteConfigString(DAILY_UPDATE_URL)

    fun getBusinessProfileUrl(): String = getRemoteConfigString(BUSINESS_PROFILE_URL)

    fun getDailyUpdateActivationHour(): String = getRemoteConfigString(DAILY_UPDATE_ACTIVATION_HOUR)

    fun getDailyUpdateActivationMinute(): String = getRemoteConfigString(DAILY_UPDATE_ACTIVATION_MINUTE)

    object FavoriteCustomer {
        fun isEnabled(): Boolean = remoteConfig.getBoolean(FAVORITE_CUSTOMER_ENABLED)

        fun getPinnedFavoriteCustomerLimit(): Long = remoteConfig.getLong(PINNED_FAVORITE_CUSTOMERS_LIMIT)

        fun getListFavoriteCustomerLimit(): Long = remoteConfig.getLong(LIST_FAVORITE_CUSTOMERS_LIMIT)

        fun getRecentCustomerLimit(): Long = remoteConfig.getLong(RECENT_CUSTOMERS_LIMIT)
    }

    fun enableHamburgerMenuBanner(): Boolean = remoteConfig.getBoolean(ENABLE_HAMBURGER_MENU_BANNER)



    fun getHamburgerMenuBannerUrl(): String = getRemoteConfigString(HAMBURGER_MENU_BANNER_URL)

    fun getLandingBannerUrl(): String = getRemoteConfigString(LANDING_DIALOG_BANNER)
    fun getLandingBannerHeader(): String = getRemoteConfigString(LANDING_DIALOG_HEADER)
    fun getLandingBannerBody(): String = getRemoteConfigString(LANDING_DIALOG_BODY)
    fun getLandingBannerButtonText(): String = getRemoteConfigString(LANDING_DIALOG_BUTTON_TEXT)

    fun getProfileTabNormalBannerUrl(): String = getRemoteConfigString(PROFILE_TAB_NORMAL_BANNER_URL_NEW)

    fun getProfileTabLendingBannerUrl(): String = getRemoteConfigString(PROFILE_TAB_LENDING_BANNER_URL_NEW)

    fun getPulsaPostpaidWaringLink(): String = getRemoteConfigString(PULSA_POSTPAID_WARNING_LINK)

    fun getProfileBannerAutoScrollTime(): Long = remoteConfig.getLong(PROFILE_BANNER_AUTOSCROLL_TIME)

    fun getReferralTnCBulletPoint(): String = getRemoteConfigString(REFERRAL_TNC_BULLET_POINT)

    fun getReferralTnCHeader(): String = getRemoteConfigString(REFERRAL_TNC_HEADER)

    fun getFloatingReferralImage(): String = getRemoteConfigString(FLOATING_REFERRAL_BUTTON_IMAGE)

    fun getMinimumPaymentAmount(): Double {
        val value = remoteConfig.getDouble(MINIMUM_PAYMENT_AMOUNT)
        if (value <= 0.0) return 10000.0
        return value
    }

    fun getMinimumPaymentOutAmount(): Double {
        val value = remoteConfig.getDouble(MINIMUM_PAYMENT_OUT_AMOUNT)
        if (value <= 0.0) return 10000.0
        return value
    }

    fun getMinimumTopupSaldoAmount(): Double {
        val value = remoteConfig.getDouble(SALDO_MIN_TOPUP_AMOUNT)
        if (value <= 0.0) return SALDO_MIN_TOPUP_AMOUNT_VALUE
        return value
    }

    fun getQrisRetryConfig(): QrisRetryConfig {
        val json = getRemoteConfigString(QRIS_RETRY_CONFIGS)
        val type = object : TypeToken<QrisRetryConfig>() {}.type
        return Utilities.jsonToObject(json, type) ?: QrisRetryConfig()
    }

    fun getPaymentConfigs(): PaymentConfigs {
        val json = getRemoteConfigString(PAYMENT_CONFIGS)
        val type = object : TypeToken<PaymentConfigs>() {}.type
        return Utilities.jsonToObject(json, type) ?: PaymentConfigs()
    }

    fun getMxMwebConfigs(): MxMwebConfigs {
        val json = getRemoteConfigString(MX_MWEB_CONFIGS)
        val type = object : TypeToken<MxMwebConfigs>() {}.type
        return Utilities.jsonToObject(json, type) ?: MxMwebConfigs()
    }

    fun getInvoiceMetadata(): InvoiceDataBlock {
        val json = INVOICE_DATA.getInvoiceDataBlock()
        val type = object : TypeToken<InvoiceDataBlock>() {}.type
        return Utilities.jsonToObject(json, type) ?: InvoiceDataBlock()
    }

    fun getPpobConfigs(): PpobConfig {
        val json = getRemoteConfigString(PPOB_CONFIG)
        val type = object : TypeToken<PpobConfig>() {}.type
        return Utilities.jsonToObject(json, type) ?: PpobConfig()
    }

    fun getEdcConfig(): EdcConfig {
        val json = getRemoteConfigString(EDC_CONFIG)
        val type = object : TypeToken<EdcConfig>() {}.type
        return Utilities.jsonToObject(json, type) ?: EdcConfig()
    }

    fun getMinSupportedVersions(): HashMap<String, String> {
        val json = getRemoteConfigString(MIN_SUPPORTED_VERSIONS)
        val type = object : TypeToken<HashMap<String, String>>() {}.type
        return Utilities.jsonToObject(json, type) ?: hashMapOf()
    }

    fun getAppText(): AppTexts {
        val json = getRemoteConfigString(APP_TEXTS)
        val type = object : TypeToken<AppTexts>() {}.type
        return Utilities.jsonToObject(json, type) ?: AppTexts()
    }

    fun physicalVisitCities(): ArrayList<String> {
        val json = getRemoteConfigString(PHYSICAL_VISIT_CITIES)
        val type = object : TypeToken<ArrayList<String>>() {}.type
        return Utilities.jsonToObject(json, type) ?: arrayListOf()
    }

    fun applyNewCompression(): Boolean = remoteConfig.getBoolean(APPLY_NEW_COMPRESSION)

    fun performVersionCheck(): Boolean = remoteConfig.getBoolean(PERFORM_VERSION_CHECK)

    fun getHamurgerMenuBannerLanding(): String = getRemoteConfigString(HAMBURGER_MENU_LANDING)

    fun retryOnConnectionFailure(): Boolean = remoteConfig.getBoolean(RETRY_ON_CONNECTION_FAILURE)

    fun getAddFavDialogText(): String = getRemoteConfigString(ADD_FAVOURITE_DIALOG)

    fun getLightningFastSaldoText(): String = getRemoteConfigString(LIGHTNING_FAST_SALDO_TEXT)

    fun getShowPaymentLandingBottomSheet(): Boolean = remoteConfig.getBoolean(PAYMENT_LANDING_BOTTOM_SHEET)

    fun enableDailyBusinessUpdate(): Boolean = remoteConfig.getBoolean(ENABLE_DAILY_BUSINESS_UPDATE)

    fun enableDailyBusinessUpdateForTesting(): Boolean = remoteConfig.getBoolean(
        ENABLE_DAILY_BUSINESS_UPDATE_FOR_TESTING
    )


    object NewBusinessCard {

        fun isEnabled() = remoteConfig.getBoolean(NEW_BUSINESS_CARD_ENABLED)
        fun getDesigns() = getRemoteConfigString(NEW_BUSINESS_CARD_DESIGN)
        fun getLockedDesigns(): List<BusinessCardDesign> {
            try {
                val json = getRemoteConfigString(BUSINESS_CARD_LOCKED_DESIGN)
                val type = object : TypeToken<List<BusinessCardDesign>>() {}.type
                var businessCard: List<BusinessCardDesign> = Gson().fromJson(json, type)
                return businessCard;
            } catch (ex: Exception) {
                ex.recordException()
                return emptyList()
            }
        }
    }

    object Referral {
        fun isContacListEnabled(): Boolean = remoteConfig.getBoolean(REFERRAL_CONTACT_LIST_ENABLED)

    }

    fun isStockToggleEnabled(): Boolean = remoteConfig.getBoolean(STOCK_ON_OFF_TOGGLE)

    fun isTncVisible(): Boolean = remoteConfig.getBoolean(TNC_VISIBLE)

    fun getTnCUrl(): String = getRemoteConfigString(TNC_URL)

    fun getPrivacyPolicyUrl(): String = getRemoteConfigString(PRIVACY_POLICY_URL)

    fun getTncTabConfig(): List<String> {
        val json = getRemoteConfigString(TAB_WITH_TNC)
        return try {
            val type = object : TypeToken<List<String>>() {}.type
            Gson().fromJson(json, type)
        } catch (ex: Exception) {
            ex.recordException()
            emptyList()
        }
    }

    fun showTransksiImage(): Boolean = remoteConfig.getBoolean(SHOW_TRANSAKSI_IMAGE)

    fun getTncTimeThreshold(): Int = remoteConfig.getLong(TNC_TIME_THRESHOLD).toInt()

    fun isUtangDueDateFeatureEnabled(): Boolean = remoteConfig.getBoolean(UTANG_DUE_DATE_ENABLE)

    fun shouldShowNewUtangInvoice(): Boolean = remoteConfig.getBoolean(SHOW_NEW_UTANG_INVOICE)

    fun shouldShowBnplFragment(): Boolean = remoteConfig.getBoolean(SHOW_SALDO_BNPL_FRAGMENT)

    fun shouldShowNewPosInvoice(): Boolean = remoteConfig.getBoolean(SHOW_NEW_POS_INVOICE)

    fun useNewOrderInvoice(): Boolean = remoteConfig.getBoolean(USE_NEW_ORDER_INVOICE)

    fun getBukuSupplierTitle(): String = getRemoteConfigString(BUKU_SUPPLIER_TITLE)

    object NewHomePage {
        fun shouldShowNewHomePage(): Boolean {
            return if (SessionManager.getInstance().isExistingOldUser) {
                remoteConfig.getBoolean(SHOW_NEW_HOME_PAGE_EXISTING_USER)
            } else {
                remoteConfig.getBoolean(SHOW_NEW_HOME_PAGE_NEW_USER)
            }
        }

        fun shouldShowFloatingButton(): Boolean = remoteConfig.getBoolean(SHOW_HOME_FLOATING_BUTTON)
        fun getFloatingButtonImage(): String = getRemoteConfigString(HOME_FLOATING_BUTTON_IMAGE)
        fun getFloatingButtonRedirection(): String = getRemoteConfigString(HOME_FLOATING_BUTTON_REDIRECTION)

        fun getHomepageBlock(): String = getRemoteConfigString(HOME_PAGE_JSON_SALDO)
        fun getHomePageFailSafeBlock(): String = getRemoteConfigString(HOME_PAGE_JSON_FAILSAFE)

        fun getHomePageBodyBlock(body_name: String): String {
            return getRemoteConfigString(body_name)
        }

        fun getHomePageBottomSheetBody(category: String): String {
            return getRemoteConfigString("${category}_bottomsheet_homepage")
        }

        fun isAutoRotateHomepageBanner(): Boolean = remoteConfig.getBoolean(
            HOMEPAGE_BANNER_AUTOROTATE
        )

        fun autoRotateHomepageBannerDuration(): Long = remoteConfig.getLong(HOMEPAGE_BANNER_DURATION)
        fun isHomeTooLargeFixEnabled(): Boolean = remoteConfig.getBoolean(HOME_TOOLARGE_FIX_ENABLED)
        fun shouldShowPromoTags(): Boolean = remoteConfig.getBoolean(SHOW_PROMO_TAGS)
        fun shouldShowPaymentHeader(): Boolean = remoteConfig.getBoolean(SHOW_PAYMENT_HEADER)
    }

    object PembayaranTabConfig {
        fun getPaymentsFragmentData(): String {
            return getRemoteConfigString(PAYMENTS_FRAGMENT_DATA)
        }

        fun getPaymentsBodyBlock(body_name: String): String {
            return getRemoteConfigString(body_name)
        }

        fun getPaymentsBottomSheetBody(): String {
            return getRemoteConfigString(PPOB_PAYMENTS_BOTTOMSHEET)
        }
    }

    private fun getRemoteConfigString(configKey: String): String {
        var originalValue = remoteConfig.getString(configKey)
        var budRemoteConfig = originalValue.replace("api-v3.bukuwarung.com","api-v4.bukuwarung.com").replace("api-v2.bukuwarung.com","api-v4.bukuwarung.com");
        return budRemoteConfig;
    }


    // transaction form configuration
    object TransactionTabConfig {
        private const val NEW_TRANSACTION_FORM_OLD_USER = "new_transaction_form_old_user"
        private const val NEW_TRANSACTION_FORM_NEW_USER = "new_transaction_form_new_user"
        private const val MERGED_TAB = "merged_transaction_tab"

        fun canShowOldTransactionForm(): Boolean {
            if (AppConfigManager.getInstance().isFirstSessionAfterInstall) {
                return !canShowNewTransactionFormToNewUser()
            }
            return !canShowNewTransactionFormToOldUser()
        }

        private fun canShowNewTransactionFormToOldUser(): Boolean {
            val newFormToOldUser = remoteConfig.getBoolean(NEW_TRANSACTION_FORM_OLD_USER)
            return newFormToOldUser
        }

        private fun canShowNewTransactionFormToNewUser(): Boolean {
            val newFormToNewUser = remoteConfig.getBoolean(NEW_TRANSACTION_FORM_NEW_USER)
            return newFormToNewUser
        }

        fun mergedTab(): Boolean {
            return remoteConfig.getBoolean(MERGED_TAB)
        }
    }

    object ReferralFloatingFeature {
        fun isFloatingButtonEnabled(): Boolean = remoteConfig.getBoolean(REFERRAL_FLOATING_BUTTON)
        fun floatingButtonVisibilityThreshold(): Long = remoteConfig.getLong(REFERRAL_FLOATING_BUTTON_VISIBILITY_THRESHOLD)
        fun floatingButtonRedirection(): String = getRemoteConfigString(REFERRAL_FLOATING_BUTTON_REDIRECTION)
        fun floatingButtonRedirectionType(): String = getRemoteConfigString(REFERRAL_FLOATING_BUTTON_REDIRECTION_TYPE)
    }

    object showTvInvited {
        fun isTvInvitedButtonEnabled(): Boolean = remoteConfig.getBoolean(SHOW_TV_INVITED)
    }

    object getTncWebviewUrl {
        fun getUrl(): String = getRemoteConfigString(TNC_WEBVIEW_URL)
    }

    object SocialMediaMarketing {
        fun getPosters(): String = getRemoteConfigString(SOCIAL_MEDIA_POSTER_DATA)
        fun getStories(): String = getRemoteConfigString(SOCIAL_MEDIA_STATUS_DATA)
    }

    object TrxBlankScreenExperiment {
        @JvmStatic
        fun getVariant(): String = getRemoteConfigString(TRX_BLANK_SCREEN_VARIANT)

        fun getVideoTutorialURL(): String = getRemoteConfigString(
            TRX_BLANK_SCREEN_EXPERIMENT_VIDEO_URL
        )
    }

    object PdfDefaultFilterExperiment {
        fun getVariant(): Int = remoteConfig.getLong(PDF_DEFAULT_FILTER_EXPERIMENT).toInt()
    }

    object DailyBusinessUpdateHighlightExperiment {
        @JvmStatic
        fun getVariant(): Boolean = remoteConfig.getBoolean(DAILY_BUSINESS_UPDATE_EXPERIMENT)
    }

    object PosExperiments {
        fun showPosOnBoarding(): Boolean = remoteConfig.getBoolean(SHOW_POS_ONBOARDING)
        fun isNonCashTaggingEnabled(): Boolean = remoteConfig.getBoolean(POS_NON_CASH_ENABLE)
        fun getPaymentWallets(): String = getRemoteConfigString(POS_PAYMENT_WALLETS)
    }

    object InventoryExperiments {
        fun isCogsAvailable(): Boolean = remoteConfig.getBoolean(INVENTORY_COGS)
    }

    object LAINNYA_PROGRESS_BAR_BLOCK {
        fun getLainnyaProgressBarBlock(): String = getRemoteConfigString(LAINNYA_PROGRESS_BAR)
    }

    object INVOICE_DATA {
        fun getInvoiceDataBlock(): String = getRemoteConfigString(INVOICE_DATA_BLOCK)
        fun getInvoiceDataFailsafeBlock(): String = getRemoteConfigString(
            INVOICE_DATA_FAILSAFE_BLOCK
        )
    }

    object APP_UPDATE_VERSION_CODE_BLOCK {
        fun getAppUpdateOrderBlock(): String = getRemoteConfigString(APP_UPDATE_VERSION_CODE)
    }

    object SelectCategory {
        fun getCreditCategories(): String = getRemoteConfigString(CATEGORY_TRANSACTION_CREDIT)
        fun getCreditCategoriesNew(): String = getRemoteConfigString(CATEGORY_TRANSACTION_CREDIT_NEW)
        fun getCreditCategoriesNewBackUp(): String = getRemoteConfigString(CATEGORY_TRANSACTION_CREDIT_NEW_BACK_UP)
        fun getDebitCategories(): String = getRemoteConfigString(CATEGORY_TRANSACTION_DEBIT)
        fun getDebitCategoriesNew(): String = getRemoteConfigString(CATEGORY_TRANSACTION_DEBIT_NEW)
        fun getDebitCategoriesNewBackUp(): String = getRemoteConfigString(CATEGORY_TRANSACTION_DEBIT_NEW_BACK_UP)

        fun getCreditCategoryDetails(): String = getRemoteConfigString(CATEGORY_TRANSACTION_CREDIT_DETAILS)
        fun getCreditCategoryDetailsNew(): String = getRemoteConfigString(CATEGORY_TRANSACTION_CREDIT_DETAILS_NEW)
        fun getDebitCategoryDetails(): String = getRemoteConfigString(CATEGORY_TRANSACTION_DEBIT_DETAILS)
        fun getDebitCategoryDetailsNew(): String = getRemoteConfigString(CATEGORY_TRANSACTION_DEBIT_DETAILS_NEW)

        fun areNewCategoriesVisible(): Boolean = remoteConfig.getBoolean(NEW_CATEGORY_VISIBLE)
    }

    fun getCategoryUIVariant(): Int = getRemoteConfigString(
        CATEGORY_UI_VARIANT
    ).toInt()

    fun getOnboardingType(): Int = getRemoteConfigString(
        ONBOARDING_TYPE).toInt()

    fun showMandatoryTransactionCategory(): Boolean = remoteConfig.getBoolean(
        SHOW_MANDATORY_TRANSACTION_CATEGORY
    )

    fun isDefaultPengeluaranStock(): Boolean = remoteConfig.getBoolean(DEFAULT_PENGELUARAN_STOCK)

    fun shouldShowAutoRecordFeature(): Boolean {
        return remoteConfig.getBoolean(SHOULD_SHOW_AUTO_RECORD_FEATURE)
    }

    fun getLoanUrl(): String = getRemoteConfigString(LOAN_URL)

    fun getNotesMissionBannerImageUrl(): String = getRemoteConfigString(NOTES_MISSION_BANNER_IMAGE)

    fun getLoyaltyMembershipUrl(): String = getRemoteConfigString(LOYALTY_MEMBERSHIP_URL)

    fun getLoyaltyPointHistoryUrl(): String = getRemoteConfigString(LOYALTY_POINT_HISTORY_URL)

    fun getLoyaltyWidgetType(): Int = remoteConfig.getLong(LOYALTY_WIDGET).toInt()

    fun showReferralEntryPointHome(): Boolean = remoteConfig.getBoolean(SHOW_REFERRAL_ENTRY_POINT_HOME)

    fun getLoyaltyLandingUrl(): String = getRemoteConfigString(LOYALTY_URL)

    fun getReferralLandingUrl(): String = remoteConfig.getString(REFERRAL_LANDING_URL)

    fun getEnableNotaMission(): Boolean = remoteConfig.getBoolean(ENABLE_NOTA_MISSION)
    fun showNewTransactionCategory(): Int = remoteConfig.getLong(SHOW_NEW_TRANSACTION_CATEGORY).toInt()

    fun getRefreshTokenNeeded(): Boolean = remoteConfig.getBoolean(IS_REFRESH_TOKEN_NEEDED)

    fun getAssistTicketPageUrl(): String = getRemoteConfigString(ASSIST_TICKET_PAGE_URL)

    fun isNewUITrxFormEnabled(): Boolean = remoteConfig.getBoolean(NEW_UI_TRX_FORM)

    fun isCrossSellPopupEnabled(): Boolean = remoteConfig.getBoolean(CROSS_ADOPTION_POPUP_ENABLED)

    object BusinessDashBoard {
        fun isTransactionCardEnabled(): Boolean = remoteConfig.getBoolean(TRANSACTION_CARD_ENABLED)
        fun isCapitalCardEnabled(): Boolean = remoteConfig.getBoolean(CAPITAL_CARD_ENABLED)
        fun isProductCardEnabled(): Boolean = remoteConfig.getBoolean(PRODUCT_CARD_ENABLED)
        fun isUtangCardEnabled(): Boolean = remoteConfig.getBoolean(UTANG_CARD_ENABLED)
        fun downloadBusinessDashBoard(): Boolean = remoteConfig.getBoolean(DOWNLOAD_BUSINESS_DASHBOARD)
        fun isPpobCardEnabled(): Boolean = remoteConfig.getBoolean(PPOB_CARD_ENABLED)
        fun isPaymentSectionEnabled(): Boolean = remoteConfig.getBoolean(PAYMENT_CARD_ENABLED)
        fun isTransactionCategoryCardEnabled(): Boolean {
            return remoteConfig.getBoolean(TRANSACTION_CATEGORY_CARD_ENABLED)
        }
    }

    object ProductCatalog {
        fun isEnabled(): Boolean = remoteConfig.getBoolean(PRODUCT_CATALOG_ENABLED)
        fun getPageSize(): Int = remoteConfig.getLong(PRODUCT_CATALOG_PAGE_SIZE).toInt()
        fun getData(): List<CatalogCategory> {
            return try {
                val type = object : TypeToken<List<CatalogCategory>>() {}.type
                val json = getRemoteConfigString(PRODUCT_CATALOG_DATA)
                Gson().fromJson(json, type)
            } catch (ex: Exception) {
                ex.printStackTrace()
                emptyList()
            }
        }

        fun getEligibleBusinessTypes(): List<Int> {
            return try {
                val type = object : TypeToken<List<Int>>() {}.type
                val json = getRemoteConfigString(PRODUCT_CATALOG_ELIGIBLE_BIZZ_TYPE)
                Gson().fromJson(json, type)
            } catch (ex: Exception) {
                ex.printStackTrace()
                emptyList()
            }
        }
    }

    fun getTimeDiffUnit(): Int = remoteConfig.getLong(TIME_DIFF_UNIT).toInt()

    fun getReferralContents(): ReferralContent {
        val json = getRemoteConfigString(REFERRAL_CONTENTS)
        val type = object : TypeToken<ReferralContent>() {}.type
        return Gson().fromJson(json, type)
    }

    fun getLoyaltyTierImages(): ConfigImages {
        val json = getRemoteConfigString(IMAGE_ASSETS_URLS)
        val type = object : TypeToken<ConfigImages>() {}.type
        return Gson().fromJson(json, type)
    }

    fun getBnplEntryPointAutoRotateDuration(): Long = remoteConfig.getLong(
        BNPL_ENTRY_AUTOROTATE_DURATION)

    fun getTickerFragmentData() : String = getRemoteConfigString(TICKER_FRAGMENT)

    fun getKomisiAgenDashboardUrl() = RemoteConfigUtils.remoteConfig.getString(KOMISI_AGEN_DASHBOARD)

    fun getKomisiAgenTermsAndConditionsUrl() = RemoteConfigUtils.remoteConfig.getString(KOMISI_AGEN_TERMS_AND_CONDITIONS)

    fun getDateTimeFormatterExp(): Boolean = remoteConfig.getBoolean(DATETIMEFORMATTER_EXP)

    fun isAuthenticationRefactor(): Boolean = remoteConfig.getBoolean(AUTHENTICATOR_REFACTOR)

    fun refreshTokenAttempt(): Int = remoteConfig.getLong(REFRESH_TOKEN_ATTEMPT).toInt()

    fun getAuthVariant(): Int = remoteConfig.getLong(AUTH_VARIANT).toInt()

    fun showInfoAtTop(): Boolean = remoteConfig.getBoolean(SHOW_INFO_AT_TOP)

    fun getTickerHeader(): String = getRemoteConfigString(TICKER_HEADER)

    fun getTickerBody(): String = getRemoteConfigString(TICKER_BODY)

    fun getCustomerCarePhoneNumber(): String = getRemoteConfigString(CUSTOMER_CARE_NUMBER)

    fun getHomePageFabConfig(): HomeFabConfig {
        val json = getRemoteConfigString(HOME_FLOATING_ACTION_BUTTON)
        val type = object : TypeToken<HomeFabConfig>() {}.type
        return Utilities.jsonToObject(json, type)?:HomeFabConfig()
    }

    fun getReferralWebContent(): ReferralWebContent {
        val json = getRemoteConfigString(REFERRAL_CONTENT_WEB)
        val type = object : TypeToken<ReferralWebContent>() {}.type
        return Utilities.jsonToObject(json, type)?: ReferralWebContent()
    }
    
    fun getRefereeReminderThreshold(): Long = remoteConfig.getLong(REFERRAL_REMINDER_THRESHOLD)

    fun getOnBoardingCampaignGameType():String= getRemoteConfigString(ONBOARDING_CAMPAIGN_GAME_TYPE)

    fun showTelephoneHelp(): Boolean {
        return remoteConfig.getBoolean(SHOW_TELEPHONE_HELP)
    }

    fun showZohodeskHelp(): Boolean {
        return remoteConfig.getBoolean(SHOW_ZOHODESK_HELP)
    }

    fun getLearnAboutAccountLevelsLink(): String {
        return remoteConfig.getString(LEARN_ACCOUNT_LEVELS)
    }

    fun getAppMaintenanceData(): String {
        return remoteConfig.getString(APP_MAINTENANCE_DATA)
    }

    fun getRefereePointEntryData(): String {
        return remoteConfig.getString(REFEREE_POINT_ENTRY_DATA)
    }

    fun getSubscriptionEntryPoint(): String {
        return remoteConfig.getString(SUBSCRIPTION_ENTRY_POINT)
    }
}

interface RemoteConfigFetchCompleteListener {
    fun onComplete()
}

