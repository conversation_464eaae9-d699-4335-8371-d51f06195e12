<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black_5">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_toolbar_title"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:paddingStart="@dimen/_0dp"
                android:paddingEnd="@dimen/_16dp"
                android:text="@string/transactions_history"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_help"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginEnd="@dimen/_16dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_baseline_help_outline"
                app:tint="@color/white" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tl_history"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:theme="@style/TabLayout_Theme"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:tabMode="scrollable"
        app:tabSelectedTextColor="@color/blue_60"
        app:tabTextAppearance="@style/FilterTabLayoutTextAppearance"
        app:tabTextColor="@color/black_40" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_search"
        style="@style/SearchTextInputLayoutStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        app:endIconTint="@color/grey_91"
        app:hintEnabled="false"
        app:layout_constraintTop_toBottomOf="@id/tl_history"
        app:startIconDrawable="@drawable/ic_search_grey"
        app:startIconTint="@color/grey_91">

        <com.google.android.material.textfield.TextInputEditText
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/search_transactions"
            android:textColor="@color/black_80"
            android:textColorHint="@color/black_20" />

    </com.google.android.material.textfield.TextInputLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_filters_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_12dp"
        app:layout_constraintTop_toBottomOf="@id/til_search">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_clear_filter"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginEnd="@dimen/_12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/hsv_filters"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_filter_history"
            tools:visibility="visible" />

        <HorizontalScrollView
            android:id="@+id/hsv_filters"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:scrollbars="none"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_clear_filter"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_filter_status"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:background="@drawable/filter_button_selector"
                    android:drawablePadding="@dimen/_8dp"
                    android:gravity="center_vertical"
                    android:padding="@dimen/_10dp"
                    android:text="@string/select_status"
                    app:drawableEndCompat="@drawable/ic_down_grey" />

                <TextView
                    android:id="@+id/tv_filter_date"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/_8dp"
                    android:background="@drawable/filter_button_selector"
                    android:drawablePadding="@dimen/_8dp"
                    android:gravity="center_vertical"
                    android:padding="@dimen/_10dp"
                    android:text="@string/select_date"
                    app:drawableEndCompat="@drawable/ic_down_grey" />

                <TextView
                    android:id="@+id/tv_filter_product"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/_8dp"
                    android:background="@drawable/filter_button_selector"
                    android:drawablePadding="@dimen/_8dp"
                    android:gravity="center_vertical"
                    android:padding="@dimen/_10dp"
                    android:text="@string/select_product"
                    app:drawableEndCompat="@drawable/ic_down_grey" />

                <TextView
                    android:id="@+id/tv_sort"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/_8dp"
                    android:background="@drawable/filter_button_selector"
                    android:drawablePadding="@dimen/_8dp"
                    android:gravity="center_vertical"
                    android:padding="@dimen/_10dp"
                    android:text="@string/sort"
                    android:visibility="gone"
                    app:drawableEndCompat="@drawable/ic_down_grey" />

                <TextView
                    android:id="@+id/tv_edc_filter"
                    style="@style/SubHeading2"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/_8dp"
                    android:background="@drawable/filter_button_selector"
                    android:gravity="center_vertical"
                    android:padding="@dimen/_10dp"
                    android:text="@string/edc_caps" />

            </LinearLayout>

        </HorizontalScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/cl_filters_container" />

    <com.bukuwarung.payments.widget.ExpiringCashbacksView
        android:id="@+id/expiring_cashback_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/cl_filters_container" />

    <com.facebook.shimmer.ShimmerFrameLayout
        android:id="@+id/sfl_shimmer_view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/expiring_cashback_info">
        <!-- Adding 7 rows of placeholders -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <include layout="@layout/item_list_payment_history_shimmer" />

            <include layout="@layout/item_list_payment_history_shimmer" />

            <include layout="@layout/item_list_payment_history_shimmer" />

            <include layout="@layout/item_list_payment_history_shimmer" />

            <include layout="@layout/item_list_payment_history_shimmer" />

            <include layout="@layout/item_list_payment_history_shimmer" />

            <include layout="@layout/item_list_payment_history_shimmer" />

            <include layout="@layout/item_list_payment_history_shimmer" />
        </LinearLayout>


    </com.facebook.shimmer.ShimmerFrameLayout>

    <TextView
        android:id="@+id/tv_result_count"
        style="@style/SubHeading1.black60"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/_16dp"
        android:paddingTop="@dimen/_16dp"
        android:paddingEnd="@dimen/_0dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/expiring_cashback_info"
        tools:text="@string/result_count"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_history"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/_60dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_result_count"
        tools:listitem="@layout/item_list_payment_history" />

    <ProgressBar
        android:id="@+id/pb_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:backgroundTint="@color/colorPrimary"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.bukuwarung.ui_component.component.empty_view.BukuEmptyView
        android:id="@+id/bev_empty_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/expiring_cashback_info"
        tools:empty_view_type="REFERRAL_NOT_USED" />

</androidx.constraintlayout.widget.ConstraintLayout>