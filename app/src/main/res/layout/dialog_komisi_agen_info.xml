<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingBottom="@dimen/_16dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_image"
        android:layout_width="192dp"
        android:layout_height="144dp"
        android:layout_marginTop="48dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_first_app_open_welcome" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_24dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/saldo_bonus_to_komisi_agen"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_image" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_24dp"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/saldo_bonus_to_komisi_agen_info"
        android:textAlignment="center"
        android:textColor="@color/black_60"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <TextView
        android:id="@+id/tv_terms_and_conditions"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="24dp"
        android:text="@string/komisi_agen_terms_and_conditions"
        android:textAlignment="center"
        android:textColor="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_subtitle" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_understand"
        style="@style/ButtonFill.Yellow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:paddingVertical="@dimen/dimen_11dp"
        android:text="@string/understand"
        android:textAppearance="@style/Heading3"
        app:cornerRadius="@dimen/dimen_10dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_terms_and_conditions" />

</androidx.constraintlayout.widget.ConstraintLayout>