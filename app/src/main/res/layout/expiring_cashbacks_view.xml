<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="@dimen/_16dp">

    <View
        android:id="@+id/vw_bg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/bg_solid_blue5_corners_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_expiring_cb_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/vw_bg"
        app:srcCompat="@drawable/ic_outline_info" />

    <TextView
        android:id="@+id/tv_expiring_cb_info"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_expiring_cb_info"
        app:layout_constraintTop_toTopOf="@id/iv_expiring_cb_info"
        tools:text="Komisi Agenmu sebesar Rp[Amount] akan kedaluwarsa pada DD-MM-YYYY. Selengkapnya" />

    <com.bukuwarung.ui_component.component.button.BukuButton
        android:id="@+id/btn_spend_cashback"
        style="@style/BukuErrorButtonOutline"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_12dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:textSize="12sp"
        app:buttonText="@string/apply_now"
        app:cornerRadius="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_expiring_cb_info" />

</androidx.constraintlayout.widget.ConstraintLayout>