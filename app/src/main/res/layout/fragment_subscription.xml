<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_home_subscription"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/colorGreyLight">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_subscribed"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="10dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:visibility="gone"
        android:background="@drawable/gradient_subscription"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_subscription"
            android:layout_width="24dp"
            android:layout_height="24dp"
            app:srcCompat="@drawable/ic_subscription"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_subscription_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="BukuWarung Plus"
            android:layout_marginStart="20dp"
            style="@style/Body3"
            app:fontFamily="@font/roboto_bold"
            android:textColor="#CE9700"
            app:layout_constraintTop_toTopOf="@+id/iv_subscription"
            app:layout_constraintStart_toEndOf="@id/iv_subscription" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_subscription_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/Body3"
            android:text="Kamu memiliki 20 kuota Komisi Agen"
            android:layout_marginEnd="5dp"
            app:layout_constraintEnd_toStartOf="@+id/iv_next"
            app:layout_constraintStart_toStartOf="@id/tv_subscription_title"
            app:layout_constraintTop_toBottomOf="@id/tv_subscription_title"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/exo_ic_chevron_right"
            app:tint="#CE9700"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_subscription_title"
            app:layout_constraintBottom_toBottomOf="@id/tv_subscription_description" />

        <ProgressBar
            android:id="@+id/pb_subscription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@id/tv_subscription_description"
            app:layout_constraintStart_toStartOf="@id/tv_subscription_title"
            app:layout_constraintEnd_toStartOf="@id/iv_next"
            style="?android:attr/progressBarStyleHorizontal" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_subscription_try"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:layout_margin="10dp"
        android:background="@drawable/gradient_subscription"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_subscription_new"
            android:layout_width="24dp"
            android:layout_height="24dp"
            app:srcCompat="@drawable/ic_subscription"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_subscription_try"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Langganan BukuWarung Plus, Yuk!"
            android:layout_marginStart="8dp"
            style="@style/Body3"
            android:textColor="#CE9700"
            android:layout_marginEnd="10dp"
            app:layout_constraintEnd_toStartOf="@id/iv_next_try"
            app:layout_constraintTop_toTopOf="@+id/iv_subscription_new"
            app:layout_constraintStart_toEndOf="@id/iv_subscription_new" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_subscription_description_try"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Dapat diskon langsung tiap pakai fitur Bayar"
            android:textColor="#5C5C5C"
            style="@style/Body3"
            android:layout_marginEnd="10dp"
            app:layout_constraintEnd_toStartOf="@id/iv_next_try"
            app:layout_constraintStart_toStartOf="@id/tv_subscription_try"
            app:layout_constraintTop_toBottomOf="@id/tv_subscription_try"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_next_try"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/exo_ic_chevron_right"
            app:tint="#CE9700"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_subscription_try"
            app:layout_constraintBottom_toBottomOf="@id/tv_subscription_description_try" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>