<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:clickable="false">

    <ImageView
        android:id="@+id/iv_icon"
        android:layout_width="@dimen/_14dp"
        android:layout_height="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginStart="@dimen/_12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:srcCompat="@drawable/ic_saldo_membership" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_title"
        style="@style/Label2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_14dp"
        android:layout_marginStart="@dimen/_4dp"
        android:maxLines="1"
        android:text="Total Komisi Agen"
        android:drawablePadding="@dimen/_10dp"
        android:drawableEnd="@drawable/ic_blue_arrow"
        app:layout_constraintTop_toTopOf="@id/iv_icon"
        app:layout_constraintStart_toEndOf="@id/iv_icon"
        app:layout_constraintVertical_chainStyle="packed"
        tools:ignore="HardcodedText" />

    <TextView
        android:id="@+id/tv_sub_title"
        style="@style/SubHeading2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:ellipsize="none"
        android:textAlignment="textStart"
        android:singleLine="false"
        android:textColor="@color/gray_80"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginBottom="@dimen/_8dp"
        app:layout_constraintStart_toStartOf="@id/iv_icon"
        app:layout_constraintTop_toBottomOf="@id/iv_icon"
        app:layout_constraintVertical_chainStyle="packed"
        tools:ignore="HardcodedText" />

</androidx.constraintlayout.widget.ConstraintLayout>