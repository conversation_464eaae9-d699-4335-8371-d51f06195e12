<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/neutral50"
    android:paddingHorizontal="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_title"
        style="@style/SubHeading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toStartOf="@+id/sw_reward"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Pakai Rp49.000 Komisi Agen" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/Body3.black40"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toStartOf="@+id/sw_reward"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="Total Komisi Agen Rp60.000" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/sw_reward"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:checked="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:thumbTint="@color/transaction_detail_switch_thumb"
        app:trackTint="@color/transaction_detail_switch_track"
        tools:ignore="UseSwitchCompatOrMaterialXml" />

    <View
        android:id="@+id/vw_line"
        android:layout_width="0dp"
        android:layout_height="@dimen/_1dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@color/black_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_subtitle" />
</androidx.constraintlayout.widget.ConstraintLayout>