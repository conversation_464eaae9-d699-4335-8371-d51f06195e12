<resources xmlns:tools="http://schemas.android.com/tools" tools:locale="en">

    <!-- Welcome screen-->
    <string name="app_name">BukuWarung</string>
    <string name="FACEBOOK_APP_ID" translatable="false">2384073191920243</string>
    <string name="indonesian">Bahasa Indonesia</string>
    <string name="english">English</string>
    <string name="malay">Malaysian</string>

    <!-- welcome slide -->
    <string name="start_verification">Start Use BukuWarung</string>
    <string name="sales">Sales</string>
    <string name="free_reminder">Remind Debt, Bill freely</string>
    <string name="data_100_secure">Data Secure and Anti Lost</string>
    <string name="used_ukm"><i>Used by \n<b>900.000</b> Business Owners</i></string>

    <!-- login screen -->
    <string name="login_input_mobile">Phone Number</string>
    <string name="send_code_to">Send Code to</string>
    <string name="login_title">Enter Application</string>
    <string name="verify_your_mobile_number">Verify Your Mobile Number</string>
    <string name="otp_counter">Sending OTP code:%1$d</string>
    <string name="otp_will_be_sent">We will send the OTP to your phone</string>
    <string name="use_whatsapp_otp">WhatsApp</string>
    <string name="use_sms_otp">SMS</string>
    <string name="please_enter_otp">Please enter the OTP you received on %1$s</string>
    <string name="no_code_sent">OTP not received?</string>
    <string name="retry_in">Try again in</string>
    <string name="retry">Try again</string>
    <string name="verifying_number">Verifying your number ...</string>
    <string name="invalid_otp">Invalid OTP</string>
    <string name="whatsapp_not_installed">Sorry, WhatsApp is not installed on your device.</string>
    <string name="ask_to_register_title">Eits, let\'s register first to use this feature</string>
    <string name="ask_to_register_subtitle">After registering, you can use <b>all features</b> in the BukuWarung application</string>
    <string name="guest_user_disabled_warning">This feature is currently unavailable</string>

    <string name="ask_to_register_title_after_trial">Want to continue taking notes? Register first</string>
    <string name="ask_to_register_subtitle_after_trial">You can take unlimited notes and everything will be saved automatically</string>

    <!-- Business Activity-->
    <!-- Business Categry dialog-->
    <string name="select_business">Select your business type</string>
    <string name="business_owner_hint">Enter your name</string>
    <string name="business_name_label">Business Name</string>
    <string name="select_business_hint">Select Business Type</string>
    <string name="next">Continue</string>
    <string name="ref_code_header">Referral Code (optional)</string>
    <string name="ref_code_hint">Referral Code</string>

    <!-- side navigation activity -->
    <string name="create_new_ledger">Add New Business</string>
    <string name="navigation_empty_text">You have not added any business yet. Add new business to record transactions in BukuWarung.</string>


    <!-- tab navigation bottom -->
    <string name="tab_customers_label">Customers</string>
    <string name="tab_reports_label">Report</string>
    <string name="tab_expense_label">Book Keeping</string>
    <string name="tab_others_label">Others</string>

    <string name="customers">Customers</string>
    <string name="debt_given">Utang Diberikan</string>
    <string name="debt_paid">Utang Dibayar</string>


    <!-- alerts !-->
    <string name="empty_business_name">Enter Business Name</string>

    <!-- Main screen -->
    <string name="dialog_choose_language">Select Language</string>

    <!-- Profile -->
    <string name="profile">Profile</string>
    <string name="edit_profile">Edit Profile</string>
    <string name="save">Save</string>
    <string name="owner_name_error">Replace with the name of the business owner</string>
    <string name="business_name_error">Must fill Business Name Correctly</string>
    <string name="business_type_error">Must fill Business Type Correctly</string>
    <string name="business_name">Business Name</string>
    <string name="business_card_owner_hint">Enter your name</string>
    <string name="business_card_shop_name">Shop Name</string>
    <string name="business_name_hint">Enter Business Name</string>
    <string name="business_owner_name_hint">Enter Your Name</string>
    <string name="pick_image_source">Select Image</string>
    <string name="summary_credit">Outstanding Credit</string>
    <string name="summary_debit">Outstanding Debit</string>
    <string name="summary_total">Net Outstanding</string>
    <string name="summary_explain_net_credit">you owe</string>
    <string name="summary_explain_net_debit">owes you</string>
    <string name="number_zero">0</string>
    <string name="activity_book_deletion_dialog_body">You can no longer see customers and transactions from this business. Are you sure you want to delete?</string>
    <string name="support">WhatsApp Us</string>
    <string name="free">Free</string>
    <string name="free_upper_case">FREE</string>
    <string name="sign_out">Log Out</string>
    <string name="share_app">Share BukuWarung</string>
    <string name="account_settings_title">Settings</string>
    <string name="account_settings">Account settings</string>
    <string name="join_fb">Join the BukuWarung FB group</string>
    <string name="bw_socmed">Bukuwarung Social Media</string>

    <!-- share app -->
    <string name="share_title">Invite friends to BukuWarung!</string>
    <string name="share_promotion">Tell your friends and family about BukuWarung. You will get free SMS points to use.</string>
    <string name="share_bukuwarung">Share BukuWarung</string>

    <!-- settings -->
    <string name="backup_settings_title"><![CDATA[Save & Synchronize]]></string>
    <string name="time_format">24 hour format</string>
    <string name="general_settings_title">General Settings</string>
    <string name="last_backup">Last Store:</string>
    <string name="backup_msg">Your transaction will be automatically saved in BukuWarung. You can restore your account with saved data if you reinstall the application.</string>
    <string name="security_setting">Security</string>
    <string name="screen_lock">Application PIN</string>
    <string name="set_screen_lock">Enable application PIN</string>
    <string name="appLanguage">Application Language</string>
    <string name="select_app_language">Select language</string>
    <string name="use_cash_feature">Use the Financial Transaction Feature</string>
    <string name="enabled_cash_explain">Use the Financial Transactions feature to monitor your income / earnings. By activating it, the Transaction feature will be added to your menu display.</string>
    <string name="update_data_now">Update Data Now</string>

    <!-- customer profile details -->
    <string name="customerProfileTitle">Customer Profile</string>
    <string name="input_customer_name_hint">Enter customer name</string>
    <string name="delete_cst_text">Delete Customer</string>
    <string name="transaction_sms">SMS transaction</string>
    <string name="transaction_sms_language">SMS language</string>

    <!-- permissions -->
    <string name="camera_permission_denied_message"> No permission to take pictures. Allow using camera to use this feature</string>
    <string name="please_wait">Please wait ...</string>
    <string name="sorry_share_referral">Sorry, can\'t share your referral right now</string>

    <!-- self remainder -->
    <string name="self_remainder">Recording Reminder</string>
    <!-- business card -->
    <string name="business_card">Business Card</string>
    <string name="ig_menu">\@bukuwarung_</string>
    <string name="fb_menu">\@bukuwarung</string>
    <string name="select_image_instruction">Select Image</string>
    <string name="cancel">Cancel</string>
    <string name="cancel_selectable">Cancel</string>
    <string name="delete">Delete</string>
    <string name="share_business_card">Share your Business Card</string>
    <string name="address_label">Address of your shop</string>
    <string name="email_label">Email Address</string>
    <string name="owner_name_label">Your Name</string>
    <string name="phone_label">Phone Number</string>
    <string name="mobile_phone_label">Mobile phone number</string>
    <string name="e_mail_label">E-mail</string>
    <string name="total_passenger">Total passenger</string>
    <string name="route_label">Route</string>
    <string name="adult">Adult</string>
    <string name="baby">Baby</string>
    <string name="title_train">Train ticket</string>
    <string name="hint_phone">Enter Phone Number</string>
    <string name="category_options">Category Selection</string>
    <string name="your_category_options">Your Category Selection</string>
    <string name="business_options">Choice of Business Type</string>
    <string name="your_business_options">Your choice of business type</string>
    <string name="object_already_exist_msg">Cannot add item. \nItems have been added before.</string>
    <string name="create_free_card">Create Free Business Cards</string>

    <!-- phonebook -->
    <string name="search_contacts">Search Contact</string>
    <string name="search_banks">Search Bank</string>
    <string name="add_customer_from_contacts">Add from Phone Contacts</string>
    <string name="choose_from_contacts">Choose from Contacts</string>
    <string name="new_phone_number">Add New Contact</string>
    <string name="new_phone_number_info">Can add customers <font color='#000000'><b>without cellphone number</b></font></string>
    <string name="existing_customer_contact">Existing customers</string>
    <string name="permission_explain_message">
         You will be able to add people from your contacts directly into BukuWarung from here
   </string>
    <string name="permission_header_message">
        Add Customers to Record Payments
   </string>

    <!-- add customer -->
    <string name="countries_in_eu">BE,BG,CZ,DK,DE,EE,IE,EL,ES,FR,HR,IT,CY,LV,LT,LU,HU,MT,NL,AT,PL,PT,RO,SI,SK,FI,SE,UK,WF,YE,ZM,ZW,VU,UY,UG,TJ</string>
    <string name="countries_prefferred_in_spinner">ID,SG,MY</string>
    <string name="bal_credit">Accept</string>
    <string name="bal_debit">Give</string>
    <string name="transaction_add_sms">Transaction recorded. WA / SMS sent to customers.</string>
    <string name="no_sms_transaction">Transaction recorded.</string>
    <string name="add">Add</string>
    <string name="customer_mobile_hint">Phone (optional)</string>
    <string name="new_customer_number">Phone (optional) )</string>
    <string name="customer_name_hint">Enter custome name</string>
    <string name="new_customer_address">Shop Address (optional)</string>
    <string name="new_customer_address_hint">Enter Business Address</string>
    <string name="input_customer_name">Customer Name</string>
    <string name="add_customer">Add Customer</string>
    <string name="add_customer_note">Record customers who owe / receivables</string>
    <string name="date">Date</string>
    <string name="credit_label">Receive Money</string>
    <string name="debit_label">Give Money</string>
    <string name="send_customer_sms">Send WA / SMS transaction notification to %1$s</string>
    <string name="new_utang_piutang">Add new credit/debit transaction</string>
    <string name="check_box_wa_prompt_title">Cancel sending notification to customer?</string>
    <string name="check_box_wa_prompt_content">You can still activate it in the next transaction.</string>
    <string name="save_utang_piutang">Save</string>
    <string name="payment_outside_operational_msg">Your transaction will be processed at the next operating hour, 07.00 - 23.00 WIB</string>
    <!-- customer tab -->
    <string name="filter_all">All</string>
    <string name="filter_debit">Debit</string>
    <string name="filter_credit">Credit</string>
    <string name="download_str">Download</string>
    <string name="credit_summary_text">Total Credit</string>
    <string name="debit_summary_text">Total Debit</string>
    <string name="credit_debit_report_label">View Credit/Debit Report</string>

    <!-- Referral Page   -->
    <string name="main_referral_title">BukuWarung Definitely Lucky</string>
    <string name="main_referral_hint">Your Referral Link</string>
    <string name="leaderboard_title">Overall Rating</string>
    <string name="referral_history_title">Point History</string>
    <string name="main_referral_h1">So the shop owner</string>

    <string-array name="main_referral_tutorials">
        <item>Share the referral code with your friends.</item>
        <item>
            As a referral recipient, make sure your friend has successfully installed and created a BukuWarung account with their mobile number.
       </item>
    </string-array>
    <string name="main_referral_share_cta">Share</string>
    <string name="main_referral_your_rank">Your Rating</string>
    <string name="main_referral_point">Points</string>
    <string name="default_placeholder" translatable="false">-</string>
    <string name="main_referral_check_leaderboard">Check Rating</string>
    <string name="main_referral_prize_title">Seize Special Prizes</string>


    <string-array name="main_referral_prizes">
        <item>Bluetooth Printer +Voucher GoPay Rp 100.000</item>
        <item>Bluetooth Printer +Voucher GoPay Rp 75.000</item>
        <item>Bluetooth Printer +Voucher GoPay Rp 50.000</item>
        <item>Voucher GoPay Rp 75.000</item>
    </string-array>

    <string name="main_referral_tnc_title">Syarat dan Ketentuan</string>

    <string-array name="main_referral_tncs">
        <item>For every transaction that you record, you will get +1 point.</item>
        <item>
            For every Referral used by your friends during registration, you and your Friends will each receive +25 Points.
       </item>
        <item>
            When your Friend (Referral Recipient) successfully registers using your Referral code then makes 3 New Transactions, then you (Referral Sender) will get EXTRA +25 Points.
       </item>
        <item>
            As a Referral Recipient, after successfully registering you will get +3 Points for every transaction made up to the first 10 transactions. Then, for the 11th transaction onwards it will be worth +1 point.
       </item>
        <item>
            Points calculation will start from the time this promo runs. Transactions that you have previously recorded will not be counted as Points.
       </item>
        <item>
            Program period: 8 June - 22 June 2020.
       </item>
    </string-array>

    <string name="leaderboard_rank_title">Rating</string>
    <string name="main_referral_empty">Sorry. \nData Not Available</string>
    <string name="points_history_placeholder" translatable="false">+ %1$s</string>
    <string name="referral_share_message">
        Grab RUPIAH MILLION REWARDS! For those of you who diligently record transactions using the BukuWarung application.
        I\'ve tried, when are you? download it here : []
   </string>
    <string name="referral_leaderboard_share_message">
        I\'m collecting points to get a million rupiah prize from BukuWarung. You can join too,
        Click here []
   </string>


    <string name="referral_empty">
        You have never referred anyone
   </string>
    <string name="null_profile_referral_title">Your Business Profile Is Incomplete</string>
    <string name="null_profile_referral_content">You must complete a business profile before you can share a referral code.</string>
    <string name="null_profile_payment_content">You must complete a business profile before you can use the Digital Payment feature.</string>
    <string name="referral_share_subtitle">
        Download the BukuWarung &amp; amp; collect your points. Get it <b>RUPIAH MILLION GIFTS</b>
   </string>
    <string name="referral_share_btn">
        Unggah ke Sosial Media
   </string>

    <!-- help center -->
    <string name="learn_bukuwarung">Tutorial</string>

    <!-- customer transactions -->
    <string name="set_due_date">Set Collection Dates</string>
    <string name="money_owed_to">You will give</string>
    <string name="money_owes_you">You will get</string>
    <string name="filter_nil">Paid</string>
    <string name="debit_sub_header">You\'ll receive</string>
    <string name="credit_sub_header">You\'ll give</string>
    <string name="amount_enter_positive_number">Enter positive number</string>
    <string name="amount_invalid_number">Invalid number</string>
    <string name="amount_warning">Enter the amount</string>
    <string name="date_warning">Enter the date</string>
    <string name="reminder_without_phone">SMS Reminder will not be sent on that date. Add a mobile number if you want to send a reminder message.</string>
    <string name="payment_due_date_title">Due date %1$s</string>
    <string name="collection_banner_title">Set collection date for %1$d customers</string>
    <string name="collection_banner_subtitle">Collect your debt %1$s %2$s fast</string>
    <string name="collection_incomplete_title">Create a debt maturity %1$d customer</string>
    <string name="collection_incomplete_subtitle">Get debt payments %1$s. %2$s which is faster</string>
    <string name="collection_free_past">Great. No overdue payments</string>
    <string name="collection_free_present">No payment collection is due today</string>
    <string name="collection_free_future">No customer with future collection date</string>
    <string name="collection_free_default">Set due date to send reminder automatically</string>
    <string name="collection_add_title">Set Collection Date</string>
    <string name="collection_add_btn">Set Collection Date</string>
    <string name="collection_complete_title">Yeay, collection dates are set!</string>
    <string name="collection_complete_subtitle">We will send automated reminder message to customers who have a phone number</string>
    <string name="collection_bottomsheet_title">Set Due</string>
    <string name="collection_bottomsheet_set">Change the due date</string>
    <string name="payment_reminder_phone">A payment reminder will be sent to this number.</string>
    <string name="dear">Mr. / Mrs.</string>
    <string name="sms_body">You still have %1$s in debt. We are waiting for your payment.</string>
    <string name="sms_footer">This message was sent via BukuWarung %1$s</string>
    <string name="payment_reminder_string">You owe %1$s per %2$s</string>
    <string name="verifiedbyapp">Verified by BukuWarung</string>
    <string name="printer_verified_by_app">Verified by BukuWarung ✓</string>
    <string name="payment_reminder">Payment Reminder</string>
    <string name="no_customer_yet">Tap <font color='#F7B500'><b>+ Debts</b></font> to make your first note</string>
    <string name="debitBtn">Give Money</string>
    <string name="add_more">Add More</string>
    <string name="creditBtn">Receive Money</string>
    <string name="customer_transaction_report">Customer Transaction Report</string>
    <string name="shareCustomerReport">Share Transaction History</string>


    <!-- app rater -->
    <string name="ok">Ok</string>
    <string name="later">Ask me later</string>
    <string name="sorry">No, Sorry</string>
    <string name="rate">Rate</string>
    <string name="rate_us_msg">
         If you enjoy using BukuWarung, please take a moment to rate us.
        \n Thanks for your support!</string>
    <string name="rate_us_title">Rate BukuWarung</string>

    <!-- search filters -->
    <string name="sort_least_amount">Least Amount</string>
    <string name="sort_most_amount">Most Amount</string>
    <string name="sort_most_recent">Most Recent</string>
    <string name="sort_name_asc">Name Asc(A-Z)</string>
    <string name="sort_name_dsc">Name Desc(Z-A)</string>

    <!-- dialogs -->
    <string name="gallery">Gallery</string>
    <string name="camera">Camera</string>
    <string name="download">Download</string>
    <string name="all">All</string>
    <string name="alert_deny_contact_permission">
         Permission to read contact is disabled. Enable it in app settings to search phonebook
   </string>

    <!-- dev consts -->
    <string name="search_hint_text">Search name, number</string>
    <string name="general_search_hint_text">Search here</string>
    <string name="menu_share_app">Share App</string>
    <string name="menu_account_settings_title">Settings</string>
    <string name="menu_privacy_policy">Privacy Policy</string>
    <string name="menu_sign_out">Logout</string>
    <string name="defaulBusinessName">BukuWarung</string>
    <string name="mybusiness">My Business</string>
    <string name="setup_profile">Setup Profile</string>
    <string name="preparing_customer_pdf">Preparing PDF documents ...</string>
    <string name="watch_tutorial">Watch Tutorial</string>

    <!-- report tab -->
    <string name="download_pdf">Download Report</string>
    <string name="download_report">Download Report</string>
    <string name="end_date">End Date</string>
    <string name="start_date">Start Date</string>
    <string name="total_transactions">%1$s Transactions</string>
    <string name="write_storage_perm_denied_error">Write to storage permission denied</string>
    <string name="total">Total</string>
    <string name="debitWithSign">Give (-)</string>
    <string name="creditWithSign">Accept (+)</string>
    <string name="trans_header_label">Transaction</string>
    <string name="category_label">Category</string>
    <string name="edit">Change</string>

    <string name="credit_debit_report">Show Debt Reports</string>
    <string name="credit_debit_report_title">"Credit/Debit Report"</string>

    <string name="today">Today</string>
    <string name="yesterday">Yesterday</string>
    <string name="lastweek">last 7 days</string>
    <string name="lastmonth">last 30 days</string>
    <string name="singleday">Select date</string>
    <string name="daterange">Select date range</string>
    <string name="cash_in_nett_summary_text">You will give</string>
    <string name="cash_out_nett_summary_text">You will receive</string>
    <string name="loss_text">Loss</string>
    <string name="profit_text">Profit</string>
    <string name="credits">You Got</string>
    <string name="debits">You Gave</string>

    <!-- collecting calendar -->
    <string name="collecting_calendar_title">Collection Dates</string>
    <string name="collecting_calendar_tooltip">Billing will be sent automatically according to the date that was created</string>
    <string name="collecting_calendar_not_paid">Not Paid</string>
    <string name="collecting_calendar_new_date">New Date</string>
    <string name="collecting_calendar_create">Create Payment Date</string>
    <string name="collecting_calendar_title_past">Past</string>
    <string name="collecting_calendar_title_present">Today</string>
    <string name="collecting_calendar_title_future">Future</string>
    <string name="collecting_calendar_amount_template">Rp. %1$s</string>
    <string name="collecting_calendar_saved">Saved Billing Date!</string>
    <string name="collecting_calendar_debt_amt">You will get: %1$s</string>
    <string name="collecting_calendar_user_count">Customer %1$d/%2$d</string>
    <string name="collecting_calendar_debt_duedate">Reminder will be sent on %1$s</string>

    <!-- privacy screen -->
    <string name="privacy_policy">Privacy Policy</string>
    <string name="store_data_question">Why data is stored online?</string>
    <string name="used_for_data_recovery">Your data is stored so that it can be used in case you loose your device.</string>
    <string name="use_for_sms_service">Your customers can receive payment reminders even if you are not using the app.</string>
    <string name="is_data_shared">Is customer data shared with other organization?</string>
    <string name="we_dont_share_data">BukuWarung doesnt share customer data with anyone, it is only used to provide you better in app experience.</string>
    <string name="html_privacy">Check our Privacy policy to know more.</string>
    <string name="activity_payments_received">Amount you received</string>
    <string name="activity_payments_given">Amount you gave</string>

    <string name="transaction_back_dialog_title">Are you sure you want to quit?</string>
    <string name="transaction_back_dialog_subtitle">Transaction logs created will not be saved.</string>

    <string name="yes">Yes</string>
    <string name="no">No</string>

    <!-- cash -->
    <string name="nominal_income">Nominal\nIncome</string>
    <string name="nominal_expense">Nominal\nSpending</string>
    <string name="nominal_modal">Cost of goods sold/\nModal</string>

    <string name="new_cash_transaction">Add Expense</string>
    <string name="income_label">Income</string>
    <string name="save_cash">Save Transaction</string>
    <string name="expense_label">Expense</string>
    <string name="add_new_categor">Add New Category</string>
    <string name="add_new_business_type">Add New Business Type</string>
    <string name="enter_category_name">Enter Category Name</string>
    <string name="byCategory">Per Category</string>
    <string name="byDate">Per Date</string>
    <string name="Daily">Daily</string>
    <string name="weekly">Weekly</string>
    <string name="monthly">Monthly</string>
    <string name="delete_category_text">Delete Category</string>
    <string name="activity_main_search_menu_most_cash_out">Most cash out</string>
    <string name="activity_main_search_menu_least_cash_out">Least cash out</string>
    <string name="activity_main_search_menu_most_cash_in">Most cash in</string>
    <string name="activity_main_search_menu_least_cash_in">Least cash in</string>
    <string name="activity_main_search_menu_name_asc">Name (A-Z)</string>
    <string name="activity_main_search_menu_name_dsc">Name (Z-A)</string>
    <string name="activity_main_search_menu_least_amount">Least Amount</string>
    <string name="activity_main_search_menu_most_amount">Most Amount</string>
    <string name="activity_main_search_menu_most_recent">Most Recent</string>
    <string name="popup_selectable_delete">Delete</string>
    <string name="popup_selectable_change">Change</string>

    <string name="edit_category_details">Edit Category Details</string>
    <string name="category_name">Category Name</string>
    <string name="transaction_deletion_dialog_title">Delete Transaction</string>
    <string name="delete_trans_body">This transaction will be deleted. Continue?</string>
    <string name="business_deletion_dialog_title">Delete Business</string>
    <string name="add_cash_in_header">Add Income</string>
    <string name="add_cash_out_header">Add Expense</string>
    <string name="transaction_print">Print</string>

    <!-- cash feature input -->
    <string name="category_delete_warning_txt">All transaction for this category will be delete.
        Do you want to continue?</string>

    <string name="select_category">Select Category</string>
    <string name="your_name_optional">Your name (optional)</string>
    <string name="business_tag_line">Slogan (optional)</string>
    <string name="location_optional">Location (optional)</string>
    <string name="email_optional">Email (optional)</string>
    <string name="edit_transaction">Edit Transaction</string>
    <string name="type">Type</string>
    <string name="no_transaction_yet_basic">
     Tap + Add Transaction to make your first transaction
   </string>
    <string name="start_btn">Start Tour</string>
    <string name="cancel_btn">Later</string>

    <string name="learn">Learn</string>

    <string name="learn_utang_feature">See the tutorial for add customer transactions</string>
    <string name="learn_cash_title">See the tutorial for making transactions</string>

    <string name="help">Need help?</string>
    <string name="tut_next_btn">Next</string>
    <string name="tut_back_btn">Back</string>

    <string name="manual_verification">Masuk Manual dengan WA</string>
    <string name="skip">Skip</string>
    <string name="preparing">Preparing Application</string>

    <string name="no_internet_connection">No Internet Connection! Try Again</string>

    <string name="tap">Tap</string>
    <string name="transaction_note_hint">Notes (optional)</string>
    <string name="credit_amount_hint">Amount Input</string>
    <string name="btn_add_credit">Add Customers</string>
    <string name="cash_transaction_report">View expense/income report</string>
    <string name="cash_out_summary_text">Expense</string>

    <string name="cash_in_profit_text">Profit</string>
    <string name="cash_in_loss_text">Loss</string>
    <string name="cash_in_buying_price">Cost of goods / capital</string>
    <string name="modal_price">Cost of goods sold</string>

    <string name="btn_add_cash_transaction">Add Transaction</string>
    <string name="date_caption">Select Report Date</string>
    <string name="credit">Credit</string>
    <string name="debit">Debit</string>
    <string name="need_help">Need Help?</string>
    <string name="transaction_deleted">Transaction deleted</string>
    <string name="share_transaction">Share Transaction</string>
    <string name="transaction_date_hint">Transaction date</string>

    <!-- notification activity -->
    <string name="entries_call">Call Shop</string>
    <string name="entries_whatsapp">WhatsApp</string>
    <string name="notes">Notes:</string>
    <string name="notes_header">Notes</string>
    <string name="notifications">Notification</string>
    <string name="no_notification">No new Notifications at this time</string>
    <string name="entries_empty">You don\'t have any notes yet</string>
    <string name="search_empty">No Search Result found</string>

    <!-- webview -->
    <string name="share">Share</string>


    <!-- whatsapp stickers -->
    <string name="download_sticker">Download</string>
    <string name="add_x_transactions">Add %1$s more transactions to unlock</string>
    <string name="unlocked_after_x_transactions">You got stickers after creating %1$s new transactions.</string>
    <string name="sticker_list_title">WhatsApp Stickers</string>

    <string name="unlock_sticker_title">Unlock Sticker</string>
    <string name="almost_there">Almost there!</string>
    <string name="just_add_x_transactions">Just add %1$s more transactions to unlock!</string>
    <string name="add_more_trans">Add more transactions</string>
    <!-- Content description for the sticker pack tray image -->
    <string name="tray_image_content_description">Sticker Pack Tray Image</string>
    <!-- Text to be shown on the button that user can click to add the sticker pack to WhatsApp -->
    <string name="add_to_whatsapp">Add to WhatsApp</string>
    <!-- Content Description for the plus button shown on sticker pack list, that allows user to add sticker pack to WhatsApp -->
    <string name="add_button_content_description">Add sticker pack to WhatsApp</string>
    <!-- Title of the sticker pack page when there are multiple sticker packs in the app -->
    <string name="title_activity_sticker_pack_details_multiple_pack">Sticker details</string>
    <!-- Text shown to the user instead of Add to WhatsApp button if the pack is already added to WhatsApp -->
    <string name="details_pack_already_added">Sticker pack added to WhatsApp</string>
    <!-- Message to show in a dialog when user fails to add sticker pack -->
    <string name="add_pack_fail_prompt_update_whatsapp">Sticker pack not added. If you\'d like to add it, make sure you update to the latest version of WhatsApp.</string>
    <!-- Text in dialog button when a user has an old version of WhatsApp that prevents them from adding the sticker pack, clicking on it will take user to play store -->
    <string name="add_pack_fail_prompt_update_play_link">Update</string>
    <!-- Toast text when we could not take user to play store because they don't have Google Play app installed -->
    <string name="cannot_find_play_store">Google Play is not installed on the device.</string>
    <string name="share_your_stickers">Share you stickers</string>
    <string name="congratulations">🎉 Congratulations! 🎉🎉🎉🎉🎉</string>
    <string name="more_trans_more_gift">Keep adding transaction to unlock more gifts</string>
    <string name="sticker_list_desc">The more fun interacting with customers using stickers</string>
    <string name="whatsapp_sticker">Sticker WhatsApp</string>
    <string name="no_transactions_found">​​Transaction not found</string>

    <string name="please_verify">Hello Admin, please help to verify my number at %1$s</string>
    <string name="wa_help_text_general">Hello BukuWarung Admin</string>

    <!-- Business Categories-->
    <string name="minimarket">Minimarket / Retail</string>
    <string name="reaturant">Reaturant / Cafe</string>
    <string name="clothing">Clothing / Fashion / Accessories</string>
    <string name="hairSalon">Hair salon / Barbershop</string>
    <string name="health">Health/Beauty shop</string>
    <string name="sportsHobby">Sports / Hobby shop</string>
    <string name="freshfood">Fresh food</string>
    <string name="vapeStore">Vape Store</string>
    <string name="electronics">Electronic Store / Phone / Pulsa shops</string>
    <string name="laundry">Laundry</string>
    <string name="logistic">Logistic</string>
    <string name="onlineShop">Online Shop</string>
    <string name="dropshipper">Dropshipping</string>
    <string name="artCraft">Art / Craft</string>
    <string name="convection">Convection</string>
    <string name="photocopy">Photocopy / Printing</string>
    <string name="bakery">Bakery / Cakeshop</string>
    <string name="drinkShop">Toko Minuman / Warkop / Thai Tea / Bubble</string>
    <string name="creditBill">Konter Pulsa, Tagihan, Aksesoris HP</string>
    <string name="coffeeshop">Coffeeshop / Bubble Tea</string>
    <string name="Bengkel">Repair Shop</string>
    <string name="penyewaanRental">Leasehold / Rental</string>
    <string name="Arisan">Meetup</string>
    <string name="mindringPeminjam">Lender</string>
    <string name="insurance">Insurance</string>
    <string name="union">Union</string>
    <string name="organization">Organization</string>
    <string name="furniture">Furniture</string>
    <string name="peddler">Peddler</string>
    <string name="agriculture">Agriculture</string>
    <string name="property">Property</string>
    <string name="storeBuilding">Store Building</string>
    <string name="perfumeShop">Perfume Shop</string>
    <string name="internetCafe">InternetCafe</string>
    <string name="frozenFood">Frozen Food</string>
    <string name="others">No Category</string>
    <string name="wholesaler">Wholesaler</string>
    <string name="distributor">Distributor</string>
    <string name="supplier">Supplier</string>
    <string name="new_business_title">Add a New Business</string>
    <string name="restoring_dont_kill_app">Urgent! Don\'t close the app while the data recovery process is running</string>
    <string name="preparing_setup">Setting up settings...</string>
    <string name="penerima">Receiver</string>
    <string name="restore_complete">Successfully Restored data!</string>
    <string name="restore_complete_btn">Done</string>

    <string name="cash_tab_trx_count">%1$d transaction</string>

    <string name="credit_sms_template_new">You have made a transaction %1$s in %2$s %3$s.\n\nCheck your transaction at : %4$s</string>
    <string name="debit_sms_template_new">Payment %1$s in %2$s %3$s already received.\n\nCheck your transaction on : %4$s</string>
    <string name="oke">Ok</string>
    <string name="off">Off</string>
    <string name="aktif">Active</string>
    <string name="delete_cst">Delete Customer</string>
    <string name="delete_cst_msg">All transactions from this customer will be deleted, along with this customer data.</string>
    <string name="tooltip_debit">Give loans or pay off debt</string>
    <string name="tooltip_credit">Receive loans or pay off accounts receivable</string>
    <string name="send_sms_notification_to">Free: Send SMS notification to</string>
    <string name="no_printer_installed">No printer installed yet</string>
    <string name="go_on">Continue</string>
    <string name="bluetooth_enable_request_msg">Bluetooth on your cellphone must be turned on in order to connect the printer.</string>
    <string name="bluetooth_enable_request_header">Turn On Bluetooth</string>
    <string name="setup_printer">printer Settings</string>
    <string name="setup_printer_command">Set up Printer</string>
    <string name="cetak">Print</string>
    <string name="total_bayar">Total Pay</string>
    <string name="print_receipt_footer">Thank you for your Trust</string>
    <string name="contact">Contact</string>
    <string name="no_printer_message">You must first install the printer in order to print receipts</string>
    <string name="new_label">New</string>
    <string name="pembayaran">Payment</string>
    <string name="transaction_date">Transaction Date: %s</string>
    <string name="total_utang">Total Debt</string>
    <string name="total_sudah_dibayar">Total already paid</string>
    <string name="kurang_bayar">Insufficient paymen</string>
    <string name="pilih_bulan">Select Month</string>
    <string name="kode_nota">Kode Nota</string>
    <string name="nomor_nota">Nomor Nota</string>

    <string name="profit">Profit</string>
    <string name="loss">Loss</string>
    <string name="product_detail">Product Details</string>
    <string name="modal_title_optional">Cost of goods / capital (optional)</string>
    <string name="additional_info">Additional Information</string>
    <string name="product_add">Add Product</string>
    <string name="product_save">Save Product</string>

    <string name="general_device_error">It looks like there is a problem with your cellphone. Please try restarting and try again</string>
    <string name="no_internet_error">Sorry, it looks like your internet connection has been lost. Please check your internet and try again.</string>
    <string name="login_exceeded_error">Sorry, you did too many login attempts. Please wait a few moments before trying again.</string>
    <string name="otp_no_internet">Sorry, it looks like your internet connection has been lost. Please check your internet and reload</string>
    <string name="otp_too_many">Sorry, you entered too many incorrect OTP codes. Please wait a few moments before trying again.</string>
    <string name="otp_error_server">Sorry, so many applications are currently being accessed. Wait a few moments and try again :)</string>
    <string name="otp_error_wrong">Incorrect OTP code. Try Again or Click the button below to enter the BukuWarung application.</string>
    <string name="change">Change</string>
    <string name="close_tutorial">Close Tutorial</string>
    <string name="rincian_produk">Product Details:</string>
    <string name="kategori_usaha">Business Category</string>
    <string name="ubah_data_pelanggan">Change Customer Data</string>
    <string name="delete_customer">Delete Customer</string>
    <string name="call_customer">Call Customer</string>


    <string name="payment_card_layout_label_digital_payment">\u0020● Digital Payment</string>
    <string name="payment_card_layout_label_digital_payment_2">Digital Payment</string>
    <string name="payment_card_dot">\u0020●</string>
    <string name="payment_label_history">History</string>
    <string name="payment_label_payment_in">Bill</string>
    <string name="payment_label_payment_out">Pay</string>
    <string name="activity_customer_list_title">Customer List</string>
    <string name="activity_customer_list_label_over_due_date">Over Due Date</string>
    <string name="activity_customer_add_from_contact">Add Customer from Contact</string>
    <string name="label_all">All</string>
    <string name="label_tempo">Tempo</string>
    <string name="label_add_new_contact">Add New Customer</string>
    <string name="dialog_add_contact_title">Add New Customer</string>
    <string name="dialog_add_contact_hint_customer_name">Enter Customer Name</string>
    <string name="dialog_add_contact_hint_customer_phone">Phone Number (not required)</string>
    <string name="label_cancel">Cancel</string>
    <string name="label_submit">Save</string>
    <string name="error_enter_contact_name">Enter Customer Name</string>
    <string name="label_payment">Payment</string>
    <string name="label_payment_only">Payment</string>
    <string name="label_payment_out">Pay</string>
    <string name="label_payment_in">Billing</string>
    <string name="label_close">Close</string>
    <string name="label_view">See</string>
    <string name="label_your_account">Your Account</string>
    <string name="label_customer_account">Customer\'s Account</string>
    <string name="error_minimum_payment_limit">Transaction minimal %s</string>
    <string name="error_saldo_topup_limit">Maximum amount of current top up %1$s</string>
    <string name="fragment_add_bank_account_title">Account Settings</string>
    <string name="label_bank">Bank</string>
    <string name="hint_select_bank">Select Bank</string>
    <string name="label_bank_account_number">Account Number</string>
    <string name="hint_numbers">***********</string>
    <string name="label_verify">Verify</string>
    <string name="label_request_payment">Billing Confirmation</string>
    <string name="fragment_add_bank_selection_error">No selected bank</string>
    <string name="fragment_add_bank_account_empty_account">Enter a bank account</string>
    <string name="label_change">Change</string>
    <string name="fragment_bank_account_list_title">Bank Account List</string>
    <string name="fragment_bank_account_customer_list_title">Bank Account Customer List</string>
    <string name="label_add_account">Add Account</string>
    <string name="label_add_account_customer">Add Customer Account</string>
    <string name="label_beneficiary_account">Beneficiary Account</string>
    <string name="menu_item_delete">Delete</string>
    <string name="label_note">Note</string>
    <string name="label_bill">Bill</string>
    <string name="delete_bank_error_exist">Cannot delete. The bank still has ongoing transactions.</string>
    <string name="delete_bank_prompt_title">Delete Bank</string>
    <string name="delete_bank_prompt_body">Are you sure you want to delete this bank?</string>
    <string name="label_transaction_fee"><![CDATA[Transaction Fees <small>(Burdened on you)</small>]]></string>
    <string name="label_transaction_fee_1">Transaction Fees</string>
    <string name="label_payment_customer_error">An error occurred while retrieving digital payment data</string>
    <string name="label_payment_in_note">Accept Digital Payments - %s</string>
    <string name="label_payment_in_note_plain">Accept Digital Payments</string>
    <string name="label_payment_method">Payment Method</string>
    <string name="fragment_payment_confirmation_title">Customer Data Summary</string>
    <string name="label_total_payment">Total Payment</string>
    <string name="label_add_contact_without_phone"><![CDATA[Can add Contacts <b>without phone number</b>]]></string>
    <string name="fragment_history_title">Payment History</string>

    <string name="payment_status_completed">Completed</string>
    <string name="succeed">Completed</string>
    <string name="payment_status_failed">Failed</string>
    <string name="label_status_transaction">Transaction Status</string>
    <string name="fragment_payment_history_pager_empty_state"><b>Well, no payment yet</b>\nPlease make a payment or use the filter to find other payment history.</string>
    <string name="label_transaction_security_guaranteed"><![CDATA[BukuWarung cooperates with partners who are licensed and supervised by <b>Bank Indonesia</b>.]]></string>
    <string name="label_transaction_security_guaranteed_plain">BukuWarung cooperates with partners who are licensed and supervised by Bank Indonesia.</string>
    <string name="label_share_invoice_link">Send Payment Link</string>
    <string name="label_help_wa_btn">Contact CS</string>
    <string name="error_default">Something went wrong, please contact our CS</string>
    <string name="bank_account_used_title">This bank account is already used as a beneficiary account in another account</string>
    <string name="bank_account_used_subtitle">For safety reason we cannot add this bank account as receiving bank account</string>
    <string name="try_other_account">Change Another Account</string>
    <string name="total_received">Total Received</string>
    <string name="total_received_by_customers">Total Received by Customers</string>
    <string name="create_payment_checkbox_text">Create Digital Link Payment, FREE admin fee</string>
    <string name="create_payment_checkbox_pay_text">Create Digital Link Payment with %s admin fee</string>

    <string name="create_pin">For New PIN</string>
    <string name="enter_otp">Enter OTP code</string>
    <string name="enter_otp_subtitle">Check OTP code that sent to SMS %s</string>
    <string name="otp_not_correct">OTP Not Correct</string>
    <string name="otp_didnt_receive">Did not receive OTP? Try Again</string>
    <string name="pin_not_correct">Pin is Incorrect</string>
    <string name="pin_forgot">Forgot PIN?</string>
    <string name="giving_label">Give</string>
    <string name="receiving_label">Receive</string>
    <string name="printer_receipt_no_address"><![CDATA[Fill in the address on the Other menu >]]></string>
    <string name="remind">Remind</string>
    <string name="sms">SMS</string>
    <string name="remind_via_other">Remind via</string>

    <string name="title_activity_self_remainder">SelfRemainderActivity</string>
    <!-- Strings used for fragments for navigation -->
    <string name="create_remainder">Create Reminders</string>
    <string name="create_new_remainder">Create a New Reminder</string>
    <string name="title_activity_set_self_remainder">SetSelfRemainder</string>
    <string name="time_to_remainder">Remind in 2 hours 6 minutes</string>
    <string name="select_remainder_category">Select a Reminder Category</string>
    <string name="remainder_sound">Reminder sound: active</string>
    <string name="remainder_title">Reminder title</string>
    <string name="category_utang_piutang">Debts</string>
    <string name="category_transaksi">Transaction</string>
    <string name="category_pembayaran">Payment</string>
    <string name="set_range">Select a range</string>
    <string name="this_month_label">This month</string>
    <string name="chose_date_range_label">SELECT DATE RANGE</string>
    <string name="empty_trx_with_filter">There are no transactions in the filter you selected. <b>Select another date filter</b> or tap <b><font color="#f7b500">+ Add Transaction</font></b> to make this month\'s transaction.</string>

    <string name="daily_due_date_notif_title">%d Customers Due Today</string>
    <string name="daily_due_date_notif_message">BukuWarung has sent an automatic reminder, don\'t forget to update their debt payment</string>
    <string name="name_label">Name</string>
    <string name="transaction_success_label">Successful Transaction</string>
    <string name="payment_successful">Payment Successful</string>
    <string name="sender">Sender</string>
    <string name="free_charge_label">Free transaction fees</string>
    <string name="search_by_category_hint">Search Category</string>
    <string name="search_by_date_hint">Search for Transactions</string>
    <string name="utang_social_message">“Since I wrote down and remembered debt using the automatic SMS feature in BukuWarung, customers paid on time.”</string>
    <string name="transaksi_social_message">“Since using BukuWarung, my business financial records have gotten tidier, so it\'s getting more profitable”</string>
    <string name="sort">Sort</string>
    <string name="search_label">Search</string>
    <string name="tampilan_label">Display</string>
    <string name="average_time_disbursement_info">Your money will be finished withdrawing in a maximum of 60 minutes.</string>
    <string name="utang_piutang_reminder">Accounts Payable Reminder</string>
    <string name="transaksi_reminder">Transaction Reminder</string>
    <string name="pembayaran_reminder">Payment Reminder</string>
    <string name="no_remainder_category_selected">Please select a category</string>
    <string name="go_setup_your_printer_header">Let\'s set up your printer first</string>
    <string name="go_setup_your_printer_message">You need to set up a bluetooth printer before you can print receipts</string>
    <string name="printing">Printing receipts...</string>
    <string name="location_permission_denied_message">Give access permission so you can print</string>
    <string name="cant_reach_printer">Cannot connect to printer!</string>
    <string name="something_when_wrong_with_code">There is an error(%1$d)</string>
    <string name="receipt_printed">Receipt printed successfully</string>
    <string name="tutorial_completion_message">Let\'s make the first bookkeeping!</string>
    <string name="select_menu">Choose one of the bookkeeping menus</string>
    <string name="record_utang">Record Debt</string>
    <string name="record_transaksi">Add Transaction</string>
    <string name="profile_redirecting_message">Let\'s make the first bookkeeping! Select \n one of the following menus</string>
    <string name="profile_success_message">Cool! Your profile is complete</string>
    <string name="double_tap_message">Press again to exit</string>

    <string name="first_transaction_dialog_heading">Congratulations! You Have Made Your First Bookkeeping!</string>
    <string name="third_transaction_dialog_heading">Congratulations! You\'ve Made 3 Notes!</string>
    <string name="fifth_transaction_dialog_heading">Congratulations! You\'ve Made 5 Notes!</string>
    <string name="first_transaction_dialog_body">Keep adding notes and get free WhatsApp stickers</string>
    <string name="third_transaction_dialog_body">Make two more notes, you can get free WhatsApp stickers</string>
    <string name="fifth_transaction_dialog_body">You can download your WhatsApp stickers now</string>
    <string name="transaction_dialog_button">MAKE A NOTE</string>
    <string name="download_stickers">DOWNLOAD STICKERS</string>
    <string name="empty_product_message">Your product list is still empty. Enter the product name and tap <b>Add Product</b></string>
    <string name="search_product">Find a product</string>
    <string name="total_product">%d Product</string>
    <string name="edit_product_name">Change the product name</string>
    <string name="delete_confirmation_title">Confirm Delete</string>
    <string name="delete_confirmation_body">You will remove this product from the product detail list.</string>
    <string name="product_already_used">The product has been used for transactions!</string>
    <string name="add_product">Add Product</string>

    <!--Onboarding-->
    <string name="reject">Reject</string>
    <string name="allow">Allow</string>
    <string name="understand">Understand</string>
    <string name="done_exclmark">Done!</string>
    <string name="try_feature">Try</string>
    <string name="new_feature">New features!</string>
    <string name="cash_transaction_intro_subtitle"><![CDATA[Select <b>transaction log type</b> Expense or Income]]></string>
    <string name="body_add_cash_btn"><![CDATA[<b>Welcome to BukuWarung!</b> Tap <b>Add Transaction</b> to calculate profit and loss for your business]]></string>
    <string name="onboarding_product_subtitle"><![CDATA[Enter a list of <b>products sold or you purchased</b> here]]></string>
    <string name="onboarding_modal_subtitle"><![CDATA[Enter the cost of the transaction so you know the profit for each transaction]]></string>
    <string name="onboarding_save_subtitle"><![CDATA[Great! You have made the first transaction, tap <b>Save Transaction</b> yes]]></string>
    <string name="onboarding_save_debt_subtitle"><![CDATA[Great! You have made the first debt record, tap <b>Save Transaction</b> yes]]></string>
    <string name="body_add_cst_btn"><![CDATA[<b>Welcome to BukuWarung!</b> Tap <b>Add Customer</b> to add new transaction for customers]]></string>
    <string name="onboarding_debt_expense"><![CDATA[Select <b>Give</b> to give loan or pay debt]]></string>
    <string name="onboarding_debt_income"><![CDATA[Select <b>Accept</b> for repayment and accept the loan]]></string>
    <string name="onboarding_debt_add_customer"><![CDATA[Add the name of the customer so remember who owes money]]></string>
    <string name="onboarding_download_report"><![CDATA[You can view and download debt reports from here]]></string>
    <string name="onboarding_download_report_cash"><![CDATA[You can view and download business financial reports here]]></string>
    <string name="onboarding_search_debt"><![CDATA[You can download reports, search and sort customers from here]]></string>
    <string name="onboarding_search_cash"><![CDATA[Search, sort, and view views per transaction per category]]></string>
    <string name="onboarding_reminder"><![CDATA[Send reminders to get <b>3x faster payments</b>]]></string>
    <string name="onboarding_calendar"><![CDATA[Set a date for sending <b>debt reminders for free</b>]]></string>
    <string name="onboarding_payment"><![CDATA[You can bill and pay debts through digital payments]]></string>
    <string name="onboarding_date_filter"><![CDATA[Select the date filter <b>to see the transaction</b> as you want]]></string>
    <string name="onboarding_product_quantity"><![CDATA[Fill in <b>number of products</b> that you are selling or buying]]></string>
    <string name="onboarding_due_date_body"><![CDATA[Set the date, BukuWarung will <b>send reminders for free!</b>]]></string>
    <string name="contact_permission_title">Give permission to import your cellphone contacts so:</string>
    <string name="contact_permission_point1">One tap to add customers</string>
    <string name="contact_permission_point2">You can send reminders for debt payments, <b>can speed up payments up to 3x</b></string>
    <string name="contact_permission_point3">Your data and privacy are guaranteed safe.</string>

    <string name="update_app">Application Update</string>
    <string name="available">Available</string>
    <string name="latest_version_app">The application has been updated</string>

    <!--Payment Tab-->
    <string name="money_in">Admission fee</string>
    <string name="money_out">Money Out</string>
    <string name="see_all">See All</string>
    <string name="see_all_payment_history">View All Payment History</string>
    <string name="payment_tab_error_state_message">It looks like your connection / signal is lost. Please check your connection / signal and try again later</string>
    <string name="payment_tab_loading_state_message">Please wait a moment yes ...</string>
    <string name="money_saved_message">BukuWarung makes you save money\u0020</string>
    <string name="payment_message_info">FREE app fee for 5x Bill or Pay!</string>
    <string name="payment_fee_info">You will be charged admin fee for every payment</string>
    <string name="lunaskan">Settle</string>
    <string name="confirmation">Confirmation</string>
    <string name="send_sms_proof">Send an SMS proof of payment</string>
    <string name="congrats_message">Congratulations!</string>
    <string name="paid_off">Paid off</string>
    <string name="payment_completion_message">already paid off</string>
    <string name="transaction_completed_message">Recorded on the BukuWarung application</string>
    <string name="enter_business_name">Enter your business name</string>

    <string name="transaction_not_found">Sorry, no transaction was found</string>
    <string name="show_customer_trx_detail">Show details of remaining debt</string>
    <string name="transaction_amount_detail_toggle">Tap to show the remaining customer debt here</string>
    <string name="pdf">PDF</string>
    <string name="excel">Excel</string>
    <string name="format_types">Format file</string>

    <string name="new_trx">Add New Transaction</string>
    <string name="invoice">Note</string>
    <string name="receipt_sharing_message_content">Hi, this is proof of your transaction. Thank you for the transaction with the shop %s.\n---\nThis message was sent by BukuWarung. Interested in using BukuWarung? Come on, download the application at https://bukuwarung.id/unduh-gratis</string>
    <string name="train_receipt_sharing_message_content">Hello, this is your proof of payment. Thank you for shopping at %s.\n\nPlease check your Train E-Ticket via the email you registered, yes.\n---\nThis message was sent via BukuWarung. Interested in using BukuWarung? Come on, download the application at https://bukuwarung.id/unduh-free</string>
    <string name="report_sharing_message">Please download the attached report.\n\nDownload the BukuWarung application to get instant and free business financial reports https://bukuwarung.com/app</string>
    <string name="referral_program">Invite Friends</string>
    <string name="enter_referral_hint">Type the code here</string>
    <string name="referral_confirmation_message">Sure the code %s what you entered is correct?</string>
    <string name="referral_confirmation_title">Confirm referral code</string>
    <string name="referral_bonus">Referral Bonus</string>

    <string name="modify_stock">Manage Stock</string>
    <string name="Stock">Stock</string>
    <string name="stock_minimum">Minimum stock</string>

    <string name="product_deletion_dialog_title">Sure delete stuff?</string>
    <string name="product_deletion_dialog_message">All data on this item will be deleted</string>

    <string name="add_stock">Addition Stock</string>
    <string name="remove_stock"> Reduction of Stock</string>
    <string name="change_items">Change Items</string>

    <string name="use_stock_feature">Use the Item Stock Feature</string>
    <string name="enabled_stock_explain">Use the Item Stock Feature to monitor the availability of items in your business. By activating it, the Stock Item feature will appear on your menu.</string>
<!--    <string name="product_stock_placeholder">Stock: %s %s</string>-->
    <string name="manage_product_stock">Set stock in the item stock menu</string>
    <string name="stock_low_label">Low stock</string>
    <string name="stock_empty_label">Out of stock</string>

    <!--Payment Details-->
<!--    <string name="renew">Update</string>-->
    <string name="refresh">Refresh</string>
    <string name="reload">Reload</string>
    <string name="choose_payment_method">Select a Payment Method</string>

    <!--Inventory Strings-->
    <string name="all_items">Current stock</string>
    <string name="all_stocks">All items</string>
    <string name="stock_running_out">Low Stock</string>
    <string name="stock">Stock</string>
    <string name="stock_home_page_heading">Usaha Saya</string>
    <string name="optional">(opsional)</string>
    <string name="minimum_stock">Minimum stock</string>
    <string name="add_product_title">Add New Items</string>
    <string name="add_new_product">Add Items</string>
    <string name="manage_stock_label">Manage Stock</string>
    <string name="stock_placeholder">Stock: %s</string>

    <string name="add_product_hint">Name of goods</string>
    <string name="custom_unit_dialog_title">New Units</string>
    <string name="custom_unit_name_hint">Type in units</string>
    <string name="custom_unit_name_length">max. 8 characters</string>
    <string name="inventory_hostory_detail_empty_screen_message">Use the set stock button to manage the number of stock items</string>
    <string name="select_unit">"Select Units" </string>
    <string name="utang_invoice_sharing_text">"This is proof of transaction in My Business, thank you for making transactions at our place. Get the best proof of transaction experience at Bukuwarung\nDownload https://bukuwarung.id/unduhGratis-u"</string>
    <string name="cash_invoice_sharing_text">"This is proof of your transaction with %s,\nIf you have a business, be like 3 million other businesses whose business has advanced with the BukuWarung application\nDownload https://bukuwarung.id/unduhGratis-t"</string>
    <string name="inventory_list_empty_screen_message">We would like to remind you if you have a transaction bill that has not been paid off %2$s in %3$s.</string>
    <string name="transaksi_berhasil_dicatat">Watch out for scams. Don\'t send money to people you don\'t know.</string>
    <string name="transaction_amount_total_label">https://bukuwarung.com/app</string>
    <string name="made_with_bukuwarung_app">Made using the BukuWarung application</string>
    <string name="bukuwarung_url">www.bukuwarung.com</string>
    <string name="content_preference_label">Content Settings</string>
    <string name="fill_address">Fill in the business address</string>
    <string name="fill_phone_number">Fill in the phone number</string>
    <string name="invoice_setting">Note Settings</string>
    <string name="paid_label">Paid off</string>
    <string name="product_name_dummy">Sample Product A</string>
    <string name="change_invoice_preference">Change Note settings</string>
    <string name="upload_logo">Upload logo</string>

    <string name="download_text">Download Business Cards</string>


    <string name="shortcut_1">info</string>
    <string name="shortcut_2">transaction</string>

    <string name="about">About BukuWarung</string>
    <string name="check_profit">See your advantage</string>

    <string name="enter_amount_in_message">Enter the bill amount (minimum IDR 10,000)</string>
    <string name="enter_amount_out_message">Enter the payment amount (minimum IDR 10,000)</string>
    <string name="nominal_you_receive">You receive the nominal</string>
    <string name="nominal_you_pay">Nominal you pay</string>
    <string name="enter_account_message">Enter the Account </string>
    <string name="bill_reminder_message">Send bill reminders to remind your customers to pay their debts on time.</string>
    <string name="nominal_bill">Nominal bill</string>
    <string name="minimum_amount_error_message">Minimum %s to share with your bank account payment link</string>
    <string name="download_tokoko_message">Create a Free Online Store</string>
    <string name="share_text_business_card">Your name card is ready</string>
    <string name="share_options">Come on, share so that your business is more famous</string>
    <string name="create_pin_message">Create your 4 digit PIN</string>
    <string name="re_enter_pin_message">Re-enter your 4 digit PIN</string>
    <string name="dont_share_pin_title">Do not share your PIN!</string>
    <string name="dont_share_pin_and_otp_body">Bukuwarung never asks for your PIN code and OTP code.</string>


    <!--Utang transactions Strings-->
    <string name="you_paid">Giving amount</string>
    <string name="i_accept">Received amount</string>
    <string name="get_it_all">Get it all</string>
    <string name="receive_full_payment">Receive full payment</string>
    <string name="optional_information">Optional Information</string>
    <string name="you_owe_total">The rest of my debt</string>
    <string name="remain_debt">Remaining debt</string>
    <string name="give_to">Give money to</string>
    <string name="Receive_from">Receiving money from</string>

    //contact module
    <string name="add_contact">Add</string>
    <string name="add_contact_hint">Customer name</string>
    <string name="import_contact">Import Contacts</string>
    <string name="contact_toolbar_title">Receive payment</string>

    <string name="not_paid">Not paid</string>
    <string name="show_details">Connect to transactions. See details</string>
    <string name="detail_transaction">Transaction Details</string>
    <string name="button_success_text">Mark paid off</string>

    <string name="transaction_tabs">Feature change!</string>
    <string name="transaction_tab_description">Now you can view transaction and debt records from here</string>
    <string name="transaction_tab_coachmark"><![CDATA[<b>Welcome to BukuWarung!</b> Select the record type of Sales or Expense transactions]]></string>
    <string name="customer_tab_coachmark"><![CDATA[<b>This is the debt tab,</b> You can record customer debts that you receive or give]]></string>
    <string name="total_transaksi">Total Transactions</string>
    <string name="status">Status</string>
    <string name="transaction_contact_debit_title">New Expenses</string>

    <string name="barang">Goods</string>
    <string name="quantity">Qty</string>
    <string name="from_splash_login">loginFromSplash</string>
    <string name="from_transaction">fromIncome</string>
    <string name="about_bukuwarung">infoBukuwarung</string>
    <string name="show_profit">showProfit</string>

    <string name="add_contact_confirmation_title">Add customer name</string>
    <string name="add_contact_confirmation_body">So that the automatic transaction is recorded as a customer debt record.</string>
    <string name="add_contact_confirm">Add Contact</string>
    <string name="total_expense_label">Total Expenses</string>
    <string name="total_selling">Total Sales</string>
    <string name="new_selling">New Sales</string>
    <string name="total_customer_unpaid_amount">Underpaid Customers: %s</string>

    <string name="profit_shortcut">Your advantage</string>
    <string name="is_transaction_paid_question_title">This transaction debt has been paid in full?</string>
    <string name="is_transaction_paid_question_body">This transaction is automatically linked with the customer\'s debt record</string>
    <string name="send_sms_title">Send proof of transaction SMS</string>
    <string name="name">Name</string>
    <string name="tab_transaction_label">Take note</string>
    <string name="connected_to_transaction">Connect to transactions</string>

    //streaks feature
    <string name="streaks_banner_dialog_heading">Make a 7 day consecutive note to get the voucher.</string>
    <string name="streaks_banner_dialog_body">Diligently note the bottom of the profit, it\'s really easy to follow 😉</string>
    <string name="streaks_banner_dialog_button">Start Taking Notes</string>
    <string name="take_more_notes">Make More Notes!</string>
    <string name="take_gift">Take a Gift</string>
    <string name="misi_juragan">Juragan Mission</string>
    <string name="read_terms_conditions">Read the terms and conditions</string>
    <string name="voucher_terms_condition">Voucher Terms and Conditions</string>
    <string name="mission_achievements">Check all your missions and achievements here</string>
    <string name="default_merchant_name">Juragan</string>
    <string name="voucher_code">Code voucher:</string>
    <string name="code_voucher">Voucher Code</string>
    <string name="voucher_terms_conditions_url">https://bukuwarung.com/p/syarat-dan-ketentuan-voucher-alfamart/</string>
    <string name="error">Error</string>

    <string name="stock_tab_empty_screen_title">New! Easy to monitor Stock</string>
    <string name="stock_empty_string_desc">The stock of items will be updated automatically when you record sales / expense transactions</string>
    <string name="stock_empty_screen_msg">Tap + <b>Add Product</b> to\nadd new stock item</string>
    <string name="stock_onboarding_desc">Monitor stock items more easily. Tap + Add Item to start.</string>
    <string name="no_stock_product_found">The item you are looking for is not in\nlist</string>
    <string name="stock_tab_switch_label_active">Stock Menu: Aktif</string>
    <string name="stock_tab_switch_label_inactive">Menu Stock: Inactive</string>
    <string name="stock_tab_disable_warning">Without a stock menu, you won\'t\nget the latest item updates.</string>
    <string name="ya">Yes</string>
    <string name="stock_tab_popup_title">Disable Stock Menu</string>

    <string name="change_product_price_message">Change the price in the item details</string>
    <string name="qty_ina">Qty</string>

    //Inventory
    <string name="add_text">Add</string>
    <string name="to_item_list">to the item list</string>
    <string name="selling_price_2">Selling price:</string>
    <string name="currency">Rp</string>
    <string name="onboarding_product_stock_zero_selling_price">Press change to set the price\nselling.</string>
    <string name="login">Sign in</string>
    <string name="info_business_category">Your business data is guaranteed to be safe from the risk of data loss or theft.</string>
    <string name="title_1">Automatic monitoring of your business performance and profits</string>
    <string name="title_2">Collect your debt 3x faster</string>
    <string name="title_3"><![CDATA[Selling and collecting debt is easy]]></string>
    <string name="subtitle_1">Record sales, income, expenses, and selling stock without the hassle.</string>
    <string name="subtitle_2">Set payment reminder date and remind customers via automated SMS message on due date.</string>
    <string name="subtitle_3"><![CDATA[Pay and receive business transactions until debt collection is free of admin fees.]]></string>
    <string name="product_sold_label_expense" >Purchased goods</string>

    // OnBoarding form error message
    <string name="biz_name_missing">Fill in your business name first, OK!</string>
    <string name="biz_category_missing">Fill in your business category first, okay!</string>
    <string name="biz_customer_type_missing">Choose your customer type first, OK!</string>
    <string name="biz_selling_method_missing">Choose your sales method first, yes!</string>

    // OnBoarding ProfileCompletion
    <string name="reseller">Reseller</string>
    <string name="personal_use">Personal Use</string>
    <string name="mutliple_choice_allowed">You can choose more than one</string>
    <string name="pilih_pelanggan_kamu_with_asterisk">Choose your customer*</string>
    <string name="selling_method_with_asterisk">Sales Method*</string>
    <string name="selling_method_offline">Offline (Sell ​​directly)</string>
    <string name="online">Online</string>

    // pulsa feature strings
    <string name="selling_price">Selling price</string>
    <string name="opsional">optional</string>
    <string name="harga_modal">Capital price</string>
    <string name="transaction_fees">Admin Fee</string>
    <string name="payment_method">Pay Method</string>
    <string name="payment_button_text">Pay - %s</string>
    <string name="indonesian_rupee">Rp</string>
    <string name="new_feature_sales_button">Try Buying Credit</string>
    <string name="payment_safe_message">BukuWarung cooperates with licensed and supervised partners <b>Bank Indonesia</b>.</string>
    <string name="inet_lost">Internet connection was lost</string>
    <string name="disturbance_message">Well, there\'s a disturbance</string>
    <string name="try_later">Currently there is interference from the provider. Try again in 1 – 2 hours.</string>
    <string name="back">Back</string>
    <string name="this_new_feature">This new feature!</string>
    <string name="onboarding_ppob"><![CDATA[Now you can sell pulses, electricity tokens and top up e-Wallets]]></string>
    <string name="produk">Product</string>
    <string name="referral_title">Invite friends, get points!</string>
    <string name="see_bonus">Lihat Bonusnya</string>
    <string name="pilih_pelanggan_kamu">Choose Your Customer</string>
    <string name="bisa_pilih_lebih_dari_satu">Can choose more than one</string>


    <string name="edit_fee_announcement">This feature is usually required for agents who need a service fee as additional information.</string>
    <string name="nominal_transaksi">Number of Transactions</string>
    <string name="service_fee">Service Fee</string>
    <string name="add_notes">Add Notes</string>
    <string name="preview">Preview</string>
    <string name="save_changes">Save Changes</string>
    <string name="write_notes">Write notes</string>
    <string name="change_bill_receipt">Change Proof of Transaction %s</string>
    <string name="payment_out_notification_heading">Let\'s finish the payment, Skipper</string>
    <string name="payment_out_notification_message">There are payments that have not been completed. Come on, send payment now.</string>
    <string name="secure_info">Digital Payment Features Guaranteed Safe, Supported by Bank Indonesia Licensed Partners</string>
    <string name="save_account_message">Save your account info</string>
    <string name="customers_pay_info">Let your customers know where to pay</string>
    <string name="customers_receive_message">Can receive or pay from various banks and e-Wallets</string>
    <string name="customers_receive_info">Transactions with a choice of any payment method <b>FREE</b> admin fee.</string>
    <string name="trx_recorded_message">Automatically recorded in the books</string>
    <string name="trx_recorded_info">Successful payments will be automatically recorded in the payment and transaction history. You don\'t need to rewrite it, okay?</string>
    <string name="check_video">Check out the Video Tutorial</string>
    <string name="buku_need_help_message">Hi BukuWarung! I need your help, here</string>
    <string name="privacy_policy1">Privacy Policy</string>
    <string name="payments_coackmark_title">Make your first payment!</string>
    <string name="payments_coachmark_message">Click <b>Bill</b> to collect payments and <b> Pay </b> to make payments.</string>
    <string name="business_name_with_asterisk">Business Name*</string>
    <string name="use_payment_feature">Use the Digital Payment Feature</string>
    <string name="enabled_payment_explain">"Use the Digital Payments Feature to pay your debts/receivables. By activating it, the Digital Payments feature will be added to your menu display. "</string>
    <string name="payment_tab_confirmation_title">Digital payment inactive</string>
    <string name="payment_tab_confirmation_subtitle">"You will deactivate the digital payment menu in the <b>main menu</b>, but you can still make digital payments in the <b>Other menu</b> "</string>
    <string name="add_customer_from_contacts_new">Contacts</string>
    <string name="ppob_outside_operational_msg">Payments cannot be made between 23.00 - 06.59 WIB every day. Try again later.</string>
    <string name="otp_will_be_sent_for_login">We will send you an OTP code to log into your account</string>
    <string name="otp_message_placeholder">OTP code has been sent via %s to number %s.</string>
    <string name="otp_change_channel_placeholder">Send code via %s</string>
    <string name="dont_kill_app_new">Urgent! Do not close the application while this process is in progress</string>
    <string name="select_business_hint_business_category">Business Category*</string>
    <string name="body_add_stock">Use the manage stock button to manage the number of items in stock</string>
    <string name="guaranteed_safe">Guaranteed Safe</string>
    <string name="invoice_settings">Note Settings</string>
    <string name="radio_credit_label">Receive</string>
    <string name="radio_debit_label">Give</string>
    <string name="payment_link_message">Please pay by bank transfer or digital wallet at the following link: %s</string>
    <string name="transaksi_report_subject">Transaction Report</string>
    <string name="utang_report_subject">Accounts Report</string>
    <string name="sort_most_stock">Most stock</string>
    <string name="sort_least_stock">Least stock</string>
    <string name="alert_error_message_on_empty_stock">Please enter stock value.</string>
    <string name="search_by_name_hint_text">Search Name</string>
    <string name="create_payment_link">Create Billing Link</string>
    <string name="share_payment_link">Share Billing Link</string>
    <string name="ask_digital_payment">Bill with digital payments</string>
    <string name="choose_account">Select Account</string>
    <string name="ask_digital_payment_message">Remind customers to pay off with the billing link so that the payment status is updated automatically</string>
    <string name="active_link_until">The payment link will be active until %s</string>
    <string name="active_link_expired">The active period of the payment link has expired, click the button to share a new link</string>
    <string name="share_new_link">Share the new link</string>
    <string name="has_paid_manually">Paid cash?</string>
    <string name="new_stickers_congratulate">Cool! Got cute stickers</string>
    <string name="keep_use_bw_more_gift">Keep using BukuWarung and look forward to more stickers</string>
    <string name="pulsa_token_listrik">Credit / Electricity Token</string>
    <string name="fashion_accessories">Clothing, Bags or Appearance Accessories</string>
    <string name="groceries_cigg">Groceries / Cigarettes</string>
    <string name="food_snacks">Food, Snack, or Drink</string>
    <string name="gallon_gas">Gallons, LPG Gas or Gasoline</string>
    <string name="minimarket_new">Fruit, Vegetable, Meat, Fish or other Fresh Food</string>
    <string name="health_beauty">Health and Beauty</string>
    <string name="repair_shop">Workshop or Repair Shop</string>
    <string name="agriculture_pertanian">Agriculture / Farm Supplies</string>
    <string name="product_sold_label">Items sold</string>
    <string name="product_purchase_label">Items purchased</string>
    <string name="no_connection_title">Internet connection dropped</string>
    <string name="no_connection_subtitle">Check your signal and try again later</string>
    <string name="server_error_title">Well, the server is busy :(</string>
    <string name="server_error_subtitle">Try it or wait a while longer</string>
    <string name="otp_sent_placeholder">Code sent via %s</string>
    <string name="label_payment_detail">Payment Details</string>
    <string name="bank_account_name">Bank Account Name</string>
    <string name="bank_account_not_found">Account not found. Make sure the account number is correct and try again.</string>
    <string name="trx_no">Transaction Code</string>
    <string name="trx_no_copied">Code copied successfully</string>
    <string name="bank_account_no">Beneficiary Account Number</string>
    <string name="customer_phone">Customer Mobile Number HP</string>
    <string name="serial_number_formatted">Serial Number: %s</string>
    <string name="text_copied">Translated Text!</string>
    <string name="proof_of_trx">Proof of payment</string>
    <string name="payment_finished_title">Hurray! %s Already Paid</string>
    <string name="you">You</string>
    <string name="create_pin_title">Create a New PIN</string>
    <string name="confirm_pin">Confirm PIN</string>
    <string name="pin_forgot_redirection">Edit</string>
    <string name="ppob_title_in_tab">It\'s more profitable to be the boss of the counter</string>
    <string name="pulsa">Pulse</string>
    <string name="electricity">Electric</string>
    <string name="data_package">Pkt. Date</string>
    <string name="enter_phone_number">Customer Mobile Number HP</string>
    <string name="enter_phone_number_hint">example: *********</string>
    <string name="price">Price</string>
    <string name="choose">Select</string>
    <string name="choose_ppob">Select nominal %s</string>
    <string name="choose_ewallet">Select E-wallet</string>
    <string name="phone_number_invalid">Unknown number! Make sure the number entered is correct</string>
    <string name="phone_number_length_invalid">Enter a 10–13 digit mobile number, OK?</string>
    <string name="most_chosen">Most voted</string>
    <string name="pay_via">Pay via </string>
    <string name="disturbance">Disturbance</string>
    <string name="sorry_disturbance">Sorry, there\'s a problem</string>
    <string name="popup_success_ppob_title">Yey! Purchase %s successful</string>
    <string name="popup_success_ppob_subtitle">Transactions are automatically recorded. Check details to see proof of transaction.</string>
    <string name="ppob_unavailable">Currently, the buy %s feature is under repair, wait for another 1-2 hours!</string>
    <string name="add_product_new">+ Add</string>
    <string name="add_manage_product">Add/Modify Items</string>
    <string name="cash_transaction_intro_amount">Tap here to enter the total sales amount</string>
    <string name="onboarding_product_subtitle_inventory_enabled"><![CDATA[Enter <b>item sold or purchased</b> here]]></string>
    <string name="onboarding_new_customer"><![CDATA[Enter customer name for this transaction]]></string>
    <string name="onboarding_date_customer_tab_filter"><![CDATA[You can filter customers by debt status]]></string>
    <string name="onboarding_single_click_settlement"><![CDATA[You can record payment of customer debt with one click</b>]]></string>
    <string name="onboarding_product_stock_detail"><![CDATA[All your items can be seen here. Tap the item name to view stock details]]></string>
    <string name="onboarding_input_bank_account_step1"><![CDATA[<b>Enter Fund Beneficiary Account</b><br/>Payment will go directly to beneficiary account]]></string>
    <string name="onboarding_input_bank_account_step2"><![CDATA[Select bank and enter beneficiary account number.]]></string>
    <string name="onboarding_connect_unpaid_to_payment"><![CDATA[Can provide payment links to customers, automatic update transaction status, and admin fees <b>FREE</b>]]></string>
    <string name="register">List</string>
    <string name="list_of_bank_account">Account List</string>
    <string name="onboarding_list_bank_subtitle"><![CDATA[You can add your account list in the More menu]]></string>
    <string name="filter_transaksi">Transaction Filter</string>
    <string name="filter_products">Product Filter</string>
    <string name="filter_status">Filter Status</string>
    <string name="fail_label">Failed</string>
    <string name="expired_label">Expired</string>
    <string name="waiting_label">Wait</string>
    <string name="success_label">Success</string>
    <string name="confirm_label">Confirmation</string>
    <string name="reset_label">Reset</string>
    <string name="all_transaction_type">All types of transactions</string>
    <string name="pay_label">Pay</string>
    <string name="bill_label">Bill</string>
    <string name="register_bank_message">Register your account to receive bills</string>
    <string name="lunaskan_sharing_message_content">Hello Mr / Mrs %1$s, thank you for paying off your debt at %2$s. Your transaction has been recorded in the BukuWarung application.  Easy and free financial recording, download https://bukuwarung.id/unduhGratis-l</string>
    <string name="product_stock_placeholder">Stock: %1$s %2$s</string>
    <string name="cash_invoice_unpaid_sharing_text">Hi %1$s, We would like to remind you if you have a transaction bill that has not been paid off %2$s in %3$s. Come on, pay your bill now!   This message was sent via the Bukuwarung Application https; //bukuwarung.com/app</string>
    <string name="unpaid_transaction_type">Transaction not paid off</string>
    <string name="streaks_rpu_heading">Diligent in taking notes, the prize is getting closer!</string>
    <string name="streaks_rpu_dialog_body">How? Make a note for 7 days in a row to get the prize 😉</string>
    <string name="modal_info_message_new">Clear the capital price if you have recorded it in your expenses so that the profit calculation is more precise.</string>
    <string name="payment_snackbar"><b>Please complete the data first!</b>👌</string>
    <string name="no_connection_message">Make sure your internet data or signal is available, then try again.</string>
    <string name="claim_gift">Take the Gift</string>
    <string name="payment_updated_info">Last modified %1$s at %2$s Automatically updated transaction details</string>
    <string name="saldo_coackmark_title">Can top up the balance for selling capital</string>
    <string name="saldo_coachmark_message">So that transactions for buying credit, electricity tokens, and e-wallet top ups can be faster.</string>
    <string name="total_penjualan">Total Sales</string>
    <string name="harga_pokok_modal">Capital Price</string>
    <string name="bulk_transaksi_info"><![CDATA[<b>Add Transaction</b> to record more. The data you fill in above will be saved automatically.]]></string>
    <string name="bulk_transaksi_header">New Transaction List</string>
    <string name="bulk_transaksi_i">So that the profit calculation is more precise, you don\'t have to fill in 2 times</string>
    <string name="tambah_detai">Add Details</string>
    <string name="profit_kamu">Your Profit</string>
    <string name="simpan_semua">Save All</string>
    <string name="category_hint_new">Write Note</string>
    <string name="product_sold_label_new">Items Sold</string>
    <string name="empty_bulk_description"><![CDATA[Come on, click <b>Add Transaction</b> to record multiple transactions at once without any hassle.]]></string>
    <string name="notes_new"><font size="13">Write a note (not mandatory)</font></string>
    <string name="bulk_dialog_title">Exit Transaction List?</string>
    <string name="bulk_dialog_body"><![CDATA[Click Save & Exit so you don\'t lose your notes.]]></string>
    <string name="bulk_dialog_yes"><![CDATA[Save & Exit]]></string>
    <string name="digi_pay_amount_message">Amount of money from Digital Payment</string>
    <string name="nama_pelanggan">Customer\'s name</string>
    <string name="service_fee_more_than_transaksi_amount">Make sure the nominal service fee is lower than the transaction amount</string>
    <string name="token_listrik">Electric Token</string>
    <string name="customer_id">PLN Meter Number / Customer ID</string>
    <string name="listrik_buy_timings_message">Purchases cannot be made at 23.00–00.30 WIB every day according to PLN regulations</string>
    <string name="enter_customer_id_hint">example: ***********</string>
    <string name="check_details_message">Make sure the data below is appropriate</string>
    <string name="customer_id_message">Customer ID</string>
    <string name="tarif">Rate/Power</string>
    <string name="listrik_mobile_number">The cellphone number is used to send the electricity token code</string>
    <string name="token_code">Token code</string>
    <string name="code_booking">Booking code</string>
    <string name="sender_account">Sender Account</string>
    <string name="receipient_detail">Recipient Details</string>
    <string name="sender_detail">Sender Details</string>
    <string name="name_of_sender">Name of the sender</string>
    <string name="name_of_recepient">Recipient\'s name</string>
    <string name="code_booking_info">Enter the booking code into the ticket printer at the departure station</string>
    <string name="electricity_token_hint">Enter the token code into the electricity meter tool. The following code has been sent via SMS</string>
    <string name="listrik_customer_id_limit_message">Enter the 8–16 digit meter number, ok?</string>
    <string name="utang_transaksi_exit_dialog_title">Exit Log Transactions?</string>
    <string name="utang_transaksi_exit_dialog_body">Notes you create will be deleted when you sign out.</string>
    <string name="utang_transaksi_exit_dialog_no_btn">Continue Record</string>
    <string name="utang_exit_dialog_title">Get Out of Record Debt?</string>
    <string name="add_contact_description">Add customers easier and faster</string>
    <string name="add_contact_title">Select Contacts</string>
    <string name="contact_permission_title_new">Approval to access and import your Contact List</string>
    <string name="phone_hint"><font size="12">Fill in mobile number to send proof of transaction</font></string>
    <string name="debt_text">Debt %1$s to me so %2$s</string>
    <string name="sales_text">My debt to %1$s is %2$s</string>
    <string name="settlement_text">Debt %s to me to be paid off</string>
    <string name="detail_customer">Customer Details</string>
    <string name="tidak_wajib">Not mandatory</string>
    <string name="utang_transaksi_exit_dialog_yes_btn">Exit</string>
    <string name="contact_header">Receive money from</string>
    <string name="contact_header_new">Give money to</string>
    <string name="decimal_qty_info">Now you can enter a decimal number for the number of items (example: 1.5)</string>
    <string name="decimal_qty_tooltip">Number of items that can be used decimal number (example: 1.5)</string>
    <string name="login_input_mobile_new">mobile number</string>
    <string name="or_label">or</string>
    <string name="otp_waiting_placeholder">Haven\'t received the code yet?</string>
    <string name="please_wait_ya">Please wait!</string>
    <string name="we_are_preparing_next_page_for_you">We are sending OTP to your number</string>
    <string name="we_are_doing_verification">We are doing verification</string>
    <string name="data_backup_found">Data backup found</string>
    <string name="we_are_restoring_your_data">We are recovering your data</string>
    <string name="data_restore_completed">Data recovery is successful!</string>
    <string name="phone_number_length_warning">Make sure your cellphone number is 10–13 digits, OK?</string>
    <string name="your_data_is_secure">Your data is safe</string>
    <string name="bosses_have_used_bw">Merchants are already using BukuWarung!</string>
    <string name="accounting_app_for_bosses">Financial Applications for the leaders</string>
    <string name="resend">resend</string>

    <!--KYC-->
    <string name="data_sent_message">Yey! Data successfully sent!</string>
    <string name="verification_time_message">We need maximum 5 working day to process your data verification.</string>
    <string name="verify_identity_for_full_service">Verify identity to get full service</string>
    <string name="verify_data_complete_premium">Data and biometric verification successful</string>
    <string name="verify_data_complete_prioritas">Data, biometric and physical store verification successful</string>
    <string name="premium_account">Membership: Premium Account</string>
    <string name="membership_priority">Membership: Priority Account</string>
    <string name="membership_premium">Membership: Premium Account</string>
    <string name="membership_standard">Membership: Standard Account</string>
    <string name="ask_do_kyc">Lets do verification!</string>
    <string name="ask_do_kyc_subtitle">Data verification will help you to continue doing transaction safely.</string>
    <string name="verify">Verify</string>
    <string name="verification_failed">Verification Failed</string>
    <string name="verification_failed_message">Please verify your data again so that you can do transaction safely.</string>
    <string name="reverify">Re-verify</string>

    <string name="collection_calendar">Collections</string>
    <string name="watch_video">Watch</string>
    <string name="video_tutorial_record_transaction">Cash transaction video tutorial</string>
    <string name="nominal_accepted">Nominal Accepted</string>
    <string name="charged_to_you">(Charged to you)</string>
    <string name="status_updated_message">Transaction status updated successfully</string>

    <!--SALDO-->
    <string name="topup_saldo">Topup Saldo</string>
    <string name="topup_saldo_bw">Topup Saldo BukuWarung</string>
    <string name="rp50k">Rp50.000</string>
    <string name="rp100k">Rp100.000</string>
    <string name="rp250k">Rp250.000</string>
    <string name="rp500k">Rp500.000</string>
    <string name="rp750k">Rp750.000</string>
    <string name="rp1000k">Rp1.000.000</string>
    <string name="min_10k">Minimal Rp10.000</string>
    <string name="or_enter_nominal_here">or enter nominal here</string>
    <string name="total_saldo_filled">Total Saldo Filled</string>
    <string name="continue_pay_and_abort_previous">There is an unpaid balance transaction. The previous transaction will be cancelled. If you have paid and the balance has not been entered, contact CS.</string>
    <string name="contact_cs">contact CS</string>
    <string name="saldo_tutorial_title">Now available Digital Saldo 🎉</string>
    <string name="saldo_tutorial_pt1_title">Do payments faster</string>
    <string name="saldo_tutorial_pt1_subtitle">It could be credit business capital, electricity tokens, e-wallet top ups, data packages, to credit and postpaid electricity.</string>
    <string name="saldo_tutorial_pt2_title">Your saldo is being kept safe</string>
    <string name="saldo_tutorial_pt2_subtitle">Your balance is safe because it is supported by partners who are supervised by the Financial Services Authority.</string>
    <string name="saldo_tutorial_pt3_title">How to easily topup saldo</string>
    <string name="saldo_tutorial_pt3_subtitle">Fill in the balance by copying the BNI BukuWarung bank virtual account. Don\'t worry, you can pay through the bank of your choice.</string>
    <string name="saldo_tutorial_vid">Check out topup saldo tutorial</string>
    <string name="not_enough_saldo">Not enough saldo</string>
    <string name="load_failed">Load Failed</string>
    <string name="saldo_in">Saldo In</string>
    <string name="saldo_out">Saldo Out</string>
    <string name="saldo_snackbar">Payment for %s is being processed!</string>

    <string name="pay_debt">Pay Debts</string>
    <string name="collect_debt">Collect Debt</string>


    <string name="order_details">Order Details</string>
    <string name="total_orders">Total Orders</string>
    <string name="receive_payment">Receive Payment</string>
    <string name="delete_order">Delete Order</string>
    <string name="all_items_customer_wants_to_buy_will_be_deleted">All the list of items that the customer wants to buy will be deleted.</string>

    <string name="make_sure_purchase_price_is_lower_than_selling_price">Make sure the purchase price is lower than the selling price</string>
    <string name="stock_profit">Profit %s</string>


    // listrik postpaid
    <string name="listrik_postpaid">Tagihan Listrik</string>
    <string name="bt_cek">Check</string>
    <string name="text_new">New</string>
    <string name="period">Period</string>
    <string name="total_billing">Total Billing</string>
    <string name="postpaid_customer_id_invalid">Enter the PLN meter number / customer ID 8 - 16 digits</string>

    <string name="send_transaction_status_message">Share payment status</string>

    <string name="pos_store_front_empty_message">Press <b>+ Add Item</b> to \nadd a customer order</string>
    <string name="come_on_take_notes_using_cashier_mode">Come on, Record Using Cashier Mode!</string>
    <string name="pos_store_front_empty_info">Let all your records be connected to the stock of goods \nd transaction records. So practical! 😉</string>
    <string name="check_email">Make sure the email address is correct</string>
    <string name="check_date_of_birth">Make sure the date, month and year are correct</string>
    <string name="financial_information">FINANCIAL INFORMATION</string>

    <string name="customer_payment_method">Customer Payment Method</string>
    <string name="pos_payment_wallet_info">This payment method is only for information to facilitate the recording of notes</string>
    <string name="pos_non_cash_payment_wallet">Non Cash - %s</string>
    <string name="non_cash">Non Cash</string>
    <string name="pos_cash_tunai">Cash/Tunai</string>

    <string name="operator">Operator</string>
    <string name="tagihan_pascabayar_capital">Pulsa Postpaid</string>

    <string name="pulsa_postpaid_title">Pulsa Pascabayar</string>
    <string name="sms_notification_that_will_be_received_by_the_customer">SMS notification that will be received by the customer</string>
    <string name="messages_that_customers_will_receive">Messages that customers will receive</string>

    <string name="total_transaction">Total Transaction</string>
    <string name="ppob_history_refund_message">If the transaction fails, the money will be returned automatically</string>
    <string name="in_process">In Process</string>
    <string name="top_up_saldo">Top up Saldo</string>
    <string name="browser_app_not_found">Browser app not found</string>
    <string name="help_center">Help Center</string>

    <string name="internet_connection_dropped">Internet connection dropped</string>
    <string name="make_sure_internet_avaialable">Make sure your internet data or signal is available, then try again.</string>

    <!--Payments Core Strings-->
    <string name="locked_account">Locked Account</string>
    <string name="create_payment">New Payment</string>
    <string name="add_bank_account">Add Bank Account</string>
    <string name="add_bank_account_subtitle_in">Let the bill payment go directly to your account.</string>
    <string name="add_bank_account_subtitle_out">Enter the account number for faster and safer payments.</string>
    <string name="payment_exit_confirmation_title">Exit Payment?</string>
    <string name="payment_exit_confirmation_subtitle">The data you have entered will be deleted when you exit.</string>
    <string-array name="payment_0_heading">
        <item>Paying with BukuWarung can save 200 times the equivalent of buying a new cellphone. Wow! 🤑</item>
        <item>Customers can pay using e-wallet or bank. So practical and immediately recorded the application 🤑</item>
        <item>So "Sophisticated skipper" by sending a note via WhatsApp. Subscriptions add more, deh! ✨</item>
        <item>More than Rp. 42 billion has been saved by the Bukuwarung skipper using the payment feature 🤑</item>
        <item>Pay and bill this-it\'s using BukuWarung digital payment so it\'s as fast as lightning ⚡</item>
        <item>Bill and pay for social gathering using BukuWarung, so it\'s sophisticated and up to date! 😎</item>
        <item>Use BukuWarung digital payment, so you don\'t have to worry about preparing change 💸</item>
        <item>Add money by selling credit, data packages, electricity, and e-wallet at BukuWarung. You can!</item>
        <item>At night, the power goes out? Don\'t worry, buying electricity tokens through BukuWarung doesn\'t have to be complicated ✨</item>
        <item>You can send digital payment links to customers, you know! No need for cash anymore, okay? 😉</item>
    </string-array>
    <string name="payment_0_tips">Tips for using BukuWarung</string>
    <string name="from_bukuwarung">from the skipper</string>
    <string name="type_customer_name">Type customer name</string>
    <string name="give_money_to">Give money to</string>
    <string name="collect_money_from">Collect money from</string>
    <string name="write_notes_optional">Write a note (optional)</string>
    <string name="send_invoice">Send Bill</string>
    <string name="send_invoice_with_amount">Send Bill - %s</string>
    <string name="pay">Pay</string>
    <string name="pay_with_amount">Pay - %s</string>

    // payment history detail
    <string name="bill_amount">The amount of the bill</string>
    <string name="receipt_sub_text">Let the customer pay and be able to check the status of the payment</string>
    <string name="notes_payment">Postscript</string>
    <string name="expired_in">Payment Deadline</string>
    <string name="payment_guide">Payment Guide</string>
    <string name="set_refund_method">Set refund method</string>
    <string name="enter_recipient_account">Enter the refund recipient\'s account or e-wallet automatically.</string>
    <string name="success_return">Your money has been successfully returned to:</string>
    <string name="refund_failed">Automatic refund failed</string>
    <string name="payment_problem">There are payment problems?</string>
    <string name="automatically_registered">Already registered automatically on the transaction menu</string>
    <string name="waiting_for_payment_status">Request Sent</string>
    <string name="in_the_process_status">Transaction in Process</string>
    <string name="expired_status">Expired</string>
    <string name="failed_status">Failed</string>
    <string name="automatic_refund_status">Automatic Refund</string>
    <string name="automatic_refund_sucess_status">Automatic Refund Successful</string>
    <string name="automatic_refund_failed_status">Automatic Refund Failed</string>
    <string name="refund_in_process">Refund In Process</string>
    <string name="refund_failed_1">Refund Failed</string>

    <string name="is_payment_done">Not yet paid?</string>
    <string name="payment_mark_unpaid">Mark Unpaid</string>
    <string name="payment_remind_unpaid">Tandai biar gak lupa ingetin pelanggan untuk bayar.</string>
    <string name="create_payment_in_link">Bill with digital payment link</string>
    <string name="send_payment_in_link">Send a link to the customer so the status is updated automatically.</string>
    <string name="create_payment_in_link_button_text">Create Payment Link</string>
    <string name="change_selling_price">Change Selling Price</string>
    <string name="amount_to_be_paid">Amount to be paid by the customer</string>
    <string name="share_link">Share Link</string>

    <string name="money_will_be_returned">Your money will be returned to:</string>
    <string name="contact_manual_refund">Please contact CS for a manual refund.</string>
    <string name="copy_text">Number copied successfully</string>
    <string name="pay_money_to">Pay Money To</string>
    <string name="transaction_amount">Transaction Amount</string>
    <string name="charged_to_the_customer">charged to the customer</string>
    <string name="cash_transaction_text">It has been automatically recorded on the transaction menu. View transaction records here</string>
    <string name="cash_transaction_text_bold">View transaction records here</string>

    <string name="payment_amount">Payment Amount</string>

    <string name="payment_category_error_message">You have not filled out the category</string>
    <string name="category_description">Category Description</string>
    <string name="voucher_code_info">You can exchange the voucher code on your favorite game website.</string>

    <!--QRIS-->
    <string name="qris_bukuwarung">QRIS BukuWarung</string>
    <string name="qris_poster_downloaded">The QRIS poster has been successfully downloaded. Check the document file</string>
    <string name="qris_main_beneficiary_account">QRIS Main Beneficiary Account</string>
    <string name="qris_code_info">QRIS is a national payment standard code created by Bank Indonesia.</string>
    <string name="accept_payments_using_qris">Accept payments using QRIS 👍</string>
    <string name="download_qris">Download QRIS</string>
    <string name="add_qris_beneficiary_account">Come on, add the recipient\'s account first!</string>
    <string name="payment_options_via_ewallet">Payment options via applications that accept the QRIS method below:</string>
    <string name="qris_download_message">So that customers can easily scan, download and print A5-sized QRIS, then paste it in the store 😉</string>
    <string name="download_qris_poster">Come on, Download Poster QRIS!</string>
    <string name="qris_application_processed">QRIS application processed</string>
    <string name="submission_includes_ktp_verification">This submission includes the ID card verification process</string>
    <string name="qris_submission_unsuccessful">QRIS submission has not been successful</string>
    <string name="qris_data_rejected_message">%1$s you failed to verify. Try re-verifying the data.</string>
    <string name="qris_business_data_rejected">Business data</string>
    <string name="qris_ktp_rejected">Account verification</string>
    <string name="qris_bank_account_rejected">set beneficiary account</string>
    <string name="add_qris_bank_message">So that transaction payments can go directly to your account.</string>
    <string name="qris_bank_success">Yey! Change the QRIS payment account successfully</string>
    <string name="qris_submit_success">The data for the QRIS submission has been sent. The data validation process takes 7 working days.</string>
    <string name="x_of_bill_amount">(%1$s of bill amount)</string>
    <string name="max_span_of_x_days">Max. span %1$d days</string>
    <string name="start_dash_end">Start - End</string>
    <string name="select_date_range_max_x_days">Select Date Rage Max. %1$d Days</string>
    <string name="qr_code">QR Code</string>
    <string name="qris_dashboard">QRIS Dashboard</string>
    <string name="filter_today">Today</string>
    <string name="filter_last_seven_days">Last Seven Days</string>
    <string name="filter_this_month">This Month</string>
    <string name="filter_pick_date">Choose Date</string>
    <string name="payment_with_qris">Payment With QRIS</string>
    <string name="already_entered_account">Already entered account</string>
    <string name="yet_to_enter_account">Haven\'t entered account</string>
    <string name="view_payment_history">View Payment History</string>
    <string name="number_of_transactions">%d Transactions</string>
    <string name="select_date">Select Date</string>
    <string name="percent">%1$.1f\%%</string>

    <!--    business profile new-->
    <string name="basic_info">Basic Information</string>
    <string name="complete_additional_info">Complete additional information so you can get a cool business card that you can send to customers .</string>
    <string name="business_operational_information">Business Operational Information</string>
    <string name="success_shop">Success Shop</string>
    <string name="business_category">Business Category</string>
    <string name="make_your_own_product">You make your own product?</string>
    <string name="select_one">Select one</string>
    <string name="yes_self_produced">👏 Yes, self-produced</string>
    <string name="buy_and_resell">🛎 Buy and resell</string>
    <string name="sell_products_yourself_and_manufacturers">💪 Sell products yourself and from manufacturers</string>
    <string name="who_buys_your_product">Who buys your product?</string>
    <string name="other_sellers">👔 Other sellers</string>
    <string name="direct_buyer">🛎 Direct buyer</string>
    <string name="monthly_turn_over">Monthly Sales Turnover</string>
    <string name="choose_monthly_turn_over">Choose monthly sales turnover</string>
    <string name="since_when">Since</string>
    <string name="choose_year_of_establishment">Choose the year of establishment of the shop/business</string>
    <string name="no_of_branch_stores">Number of Branch Stores</string>
    <string name="select_no_of_branch">Select number of branch stores</string>
    <string name="no_of_employees">Number of employees</string>
    <string name="choose_no_of_employees">Choose the number of employees</string>
    <string name="choose_opening_hours">Choose Opening Hours</string>
    <string name="choose_business_address">Tuliskan Alamat Usaha</string>
    <string name="basic_detail">Informasi Usaha</string>
    <string name="enter_basic_info">Enter your basic business information</string>
    <string name="less_than_five">Less than IDR 5,000,000</string>
    <string name="five_to_ten">Rp5.000.000 - Rp10.000.000</string>
    <string name="ten_to_twenty_five">Rp10.000.000 - Rp25.000.000</string>
    <string name="twenty_five_to_fifty">Rp25.000.000 - Rp50.000.000</string>
    <string name="more_than_fifty">Lebih dari Rp50.000.000</string>
    <string name="no_branch_store">No branch store</string>
    <string name="one_branch_store">1 Branch Store</string>
    <string name="two_branch_store">2 Branch Store</string>
    <string name="three_branch_store">3 Branch Store</string>
    <string name="more_than_three_branch">More than 3 Branch Stores</string>
    <string name="privately_managed">Privately Managed</string>
    <string name="one_employee">1 Employee</string>
    <string name="two_to_three_employee">2 – 3 Employee</string>
    <string name="four_to_ten_employee">2 – 3Employee</string>
    <string name="more_than_ten_employee">More than 10 Employees</string>
    <string name="bp_success">Hurray! You have successfully completed the shop information</string>
    <string name="bp_succcess_info">Now you can create a special business card design with your store information, you know.</string>

    <string name="want_to_continue_transaction">Want to continue the transaction?</string>
    <string name="more">More</string>

    <string name="account_verification">Account Verification</string>
    <string name="verify_now">Verify Now Sekarang</string>
    <string name="account_verification_now">Account Verification Now</string>
    <string name="pay_up">Pay Up</string>
    <string name="card_number">Card number</string>
    <string name="no_of_family">Number of Family</string>
    <string name="invalid_card_number">Enter the family VA number 11–16 digits, ok?</string>
    <string name="select_payment_period">Select Payment Period</string>
    <string name="rrn_qris">RRN QRIS</string>
    <string name="refund_bank_account">Refund Bank Account</string>
    <string name="open">Open</string>


    <string name="merchant_id">Merchant ID</string>
    <string name="select_problem">Please select the problem you are experiencing</string>
    <string name="constraints_do_not_match">Constraints do not match?</string>
    <string name="chronology_problem">Tell the chronology of the problem to our team</string>
    <string name="assist_title">Transaction Constraints</string>
    <string-array name="assist_problems">
    <item>Payment failed. I want to request a refund</item>
    <item>My transaction is pending for more than 5 minutes</item>
    <item>My transaction was debited 2 times</item>
    </string-array>
    <string name="app_version">App Version</string>
    <string name="packet_data_title">Packet Data</string>
    <string name="title_pdam">PDAM</string>
    <string name="select_region">Select region</string>
    <string name="customer_number">Customer number</string>
    <string name="pdam_area">PDAM area</string>
    <string name="search_region">Search region</string>
    <string name="not_available">being disturbed</string>
    <string name="customer_number_validation_error">Enter the customer number 4-20 digits, yes</string>
    <string name="empty_text">The area you are looking for was not found...</string>
    <string name="fine">Fine</string>
    <string name="change_name_of_business">Change the name of the business first, yes!</string>
    <string name="change_name_of_business_msg">The word %1$s has been used by another institution/contains inappropriate words. Please change to continue the transaction.</string>
    <string name="change_name_of_business_msg_2">The word %1$s is already used by another institution/contains inappropriate words. Please change another name</string>
    <string name="image_app_not_found">The application is not installed to open the image.</string>

    <string name="processed_from_payment_tab">Processed from the payment menu</string>
    <string name="mobile_added_message">The customers mobile number has been added successfully</string>
    <string name="transaction_marked_paid">👌 Transaction has been marked paid off</string>
    <string name="transaction_number">Transaction Number</string>
    <string name="qris_account_locked_warning">Your transaction failed because the account is locked. <b>Select another account and try the transaction again.</b></string>
    <string name="here">here</string>
    <string name="please_try_again_after">If it still fails, please try again after %1$s %2$s</string>
    <string name="not_enough_balance">Your balance is not enough to continue the payment.</string>
    <string name="qris_bank_updated">The QRIS recipient\'s account has been successfully changed. Please do the transaction again by pressing try again.</string>
    <string name="cant_be_processed">Can\'t be processed</string>
    <string name="un_supported_text">For payment security, we lock this account. Please select another account.</string>
    <string name="bank_not_supported_for_refund">Refunds to this bank cannot be processed. Try choosing another account</string>
    <string name="bank_disabled_for_refund">Refunds cannot be processed because this account is locked. Please change to another account.</string>
    <string name="total_saldo">Total Saldo</string>
    <string name="description">Description</string>
    <string name="conversion_id">Conversion ID</string>
    <string name="switch_business_book">Switch Business Book</string>
    <string name="qris_available_in_book_title">The QRIS feature is only available in the %1$s business book</string>
    <string name="qris_available_in_book_description">To continue, please switch to the business book %1$s and the transaction will be recorded there.</string>
    <string name="qris_book_deletion_error_title">Important Information for Landlords!</string>
    <string name="qris_book_deletion_error_message">You cannot delete a business that has been registered in the QRIS feature.</string>
    <string name="show_receipt">Show Transaction Proof</string>
    <string name="hide_receipt">Hide Proof of Transaction</string>
    <string name="create_store_qris_qr">Create your store\'s QRIS code here</string>
    <string name="qris_failed_try_again">QRIS submission failed. Try again, ok?</string>
    <string name="kyc_failed_try_again">KYC verification failed. Please try again.</string>
    <string name="account_is_loacked_change_first">Your account is locked. Change first, okay</string>
    <string name="congratulations_you_have_completed_your_profile">Yey! Kamu berhasil lengkapi info Profil kamu</string>

    <string name="data_rekening_penerima_sudah_sesuai">Continue to pay with this beneficiary\'s account?</string>
    <string name="payment_confirm_detail">If you have, the bill payment will go into the account of the recipient of %1$s (%2$s - %3$s)</string>
    <string name="batal">cancelled</string>
    <string name="ya_sesuai">Yes, that\'s right</string>

    <!--Recents and Favourites-->
    <string name="add_favourite_customer">Add customer to favorites</string>
    <string name="favourite_subtitle">Lets pay the next 2x faster</string>
    <string name="favourite">Favorite</string>
    <string name="favourite_contact">Favorite Contact</string>
    <string name="enter_name">Enter Name</string>
    <string name="favourite_title">Favorite Customer</string>
    <string name="remove_favourite">Remove from favorites?</string>
    <string name="remove_bank">Delete Bank Account?</string>
    <string name="remove_favourite_subtitle">Selected subscribers will be removed from the favorites list.</string>
    <string name="remove_bank_subtitle">The bank account will be deleted on the last payment list.</string>
    <string name="same_name_error">This name is already in use. Try changing it to another name</string>
    <string name="choose_favourite">Choose from the list for quick access ⭐</string>
    <string name="empty_fav_heading">Wow, no favorite customer yet</string>
    <string name="empty_fav_sub_heading">Add favorite customers to make the next payment faster and more convenient.</string>
    <string name="empty_recent_heading">No payment yet</string>
    <string name="empty_recent_sub_heading">Come on, chase profits by selling credit and bills with the BukuWarung application.</string>
    <string name="add_favourite">Add Favorite</string>
    <string name="recent_transaction">Last payment</string>
    <string name="fav_on_boarding">Just press star to add favorite subscriber. Saved right away!</string>
    <string name="fav_on_boarding_title">Add Favorite Customer</string>
    <string name="recent_see_all">View all transaction history</string>
    <string name="add_favourite_success_msg">Successfully added favorite subscriber</string>
    <string name="remove_favourite_success_msg">Successfully deleted favorite subscriber</string>
    <string name="platform_fee">Application Fee</string>
    <string name="x_left">%1$dx left</string>
    <string name="what_is_platform_fee">What is application fee?</string>
    <string name="platform_fee_info">Platform fees are costs that we maximize to improve the best service for you.</string>
    <string name="learn_more">Learn more</string>
    <string name="verify_premium_account">Come on, verify your account first!</string>
    <string name="verify_premium_account_smile">Come on, verify your account first! 😃</string>
    <string name="kyc_account_benefits"><![CDATA[Account verification benefits:<ul><li> &#160; Safer account protection with the latest innovations in biometric technology from VIDA </li><li> &#160; Can Bill and Pay with a bigger limit</li><li> &#160; Receive payments using QRIS and can withdraw money to the account on the same day</li><li> &#160; Easier access to Business Capital Solution loans</li><li> &#160; Selling Digital Products such as credit, electricity tokens, and bills using Balance with a limit of up to Rp. 100 million </li> </ul>]]></string>
    <string name="cashback">Cashback</string>
    <string name="transaction">Transaction</string>
    <string name="saldo_cashback_credit_info">Cashback will be credited to Bukuwarung balance 1x24 hours after the transaction is successful.</string>
    <string name="select">Select</string>
    <string name="qris_submission_in_process">Please wait! Submission is being processed</string>

    <!--    MultiFinance Strings-->
    <string name="title_multifinance">Credit Installment</string>
    <string name="multifinance">Credit\nInstallment</string>
    <string name="contract_number">Contract number</string>
    <string name="multifinance_hint">Choose a Loan Provider</string>
    <string name="loan_provider">Loan Provider</string>
    <string name="search_service">Search service</string>
    <string name="multifinance_disturbance">There is a disturbance</string>
    <string name="installment_number">Installment Number</string>

    <string name="bill_list">Bill List</string>
    <string name="paid">Paid</string>
    <string name="due_date">%s - Due Date %s</string>
    <string name="remind_button">remind</string>
    <string name="no_one_paid_bill">Wow, no one has paid the bill yet</string>
    <string name="remind_customer">Come on, remind customers to make a profit</string>
    <string name="customer_not_found">Customer not found</string>
    <string name="try_changing_another_name">Try changing the name of another customer, ok?</string>
    <string name="bayar">Pay</string>
    <string name="billing_reminder">Routine Billing Reminder</string>


    <!--CRM Reminders-->
    <string name="reminder">Reminder</string>
    <string name="bill_reminder">Bill Reminder</string>
    <string name="make_transaction">Make Payment</string>
    <string name="send_reminder">Send Reminder</string>
    <string name="remind_again">Remind Again</string>
    <string name="bill_already_paid">Bill Already Paid</string>
    <string name="electricity_bill_details">Electricity Bill Details</string>
    <string name="postpaid_billing_details">Postpaid Billing Details</string>
    <string name="pdam_biiling_details">PDAM Billing Details</string>
    <string name="payment_expiration">Payment Expiration</string>
    <string name="create_new_pay_to_continue">To continue, please create a new payment.</string>
    <string name="make_new_payment">Make a New Payment</string>

    <!--Liveliness kyc-->
    <string name="liveliness_landing_point_1">Make sure the person who does the loan verification is the same as the ID verification data in the previous BukuWarung.</string>
    <string name="liveliness_landing_point_2">Make sure you are in a bright room and do not turn your back on the light</string>
    <string name="liveliness_landing_point_3">The face must be original, clear, not covered by anything and in the specified area</string>
    <string name="cara_ambil_selfie_kamu">How to take your selfie</string>
    <string name="contoh">Example:</string>
    <string name="benar">Right</string>
    <string name="mulai">Start</string>
    <string name="posisikan_wajah_di_area_bawah_ini">Position the face in the area below</string>
    <string name="description_liveliness">Make sure you are in a bright room and do not turn your back on the light</string>
    <string name="description2_liveliness">Make sure the face is clearly visible and not\ncovered by anything</string>
    <string name="title_liveliness">Confirm with Selfie</string>
    <string name="image_processing">Photos are in the process of checking&#8230;</string>
    <string name="image_error">The selfie analysis process hasn\'t worked yet</string>
    <string name="image_success_tick">The selfie analysis process was successful!✅</string>
    <string name="image_success_description">Please send and get ready for the next process.</string>
    <string name="image_failed_description">Please resend your selfie for re-analysis.</string>
    <string name="image_taking_time_description2">The photo matching process is taking too long. Try\ndo this process again..</string>
    <string name="image_failed_description2">The photo you uploaded is not a facial selfie. Try doing this process again.</string>
    <string name="image_exhaust_description2">You have many failed attempts. Retry limit reached</string>
    <string name="send">Send</string>
    <string name="txt_return">Return</string>
    <string name="take_pic">Take a picture</string>
    <string name="data_security">Your data is stored safely</string>
    <string name="data_security_desc">Your data is only used according to regulations.</string>
    <string name="data_security_link">Learn about privacy data</string>
    <string name="salah">Wrong</string>
    <string name="help_bantuan">Help</string>
    <string name="change_beneficiary_account">Please Change Beneficiary Account</string>
    <string name="change_beneficiary_account_message">Let the bill payment be forwarded to your account</string>
    <string name="retransaction_failed">Re-transaction failed</string>
    <string name="contact_support_for_disbursal">Please contact CS to receive bill payments manually.</string>
    <string name="payment_will_be_forwarded_to">Payment will be forwarded to:</string>
    <string name="payment_to_account_failed_try_another">Payments to this bank account cannot be processed. Please select another bank account.</string>
    <string name="payment_successfully_forwarded_to">Payment successfully forwarded to:</string>
    <string name="refund_process_success">Refunds are being processed by our system. Usually takes a maximum of 3 hours.</string>

    <!--    Internet and Tv Cable Strings-->
    <string name="title_internet_dan_tv_cable">Internet &amp; Cable TV</string>
    <string name="provider">Provider</string>
    <string name="internet_dan_tv_cable_provider">Provider Internet &amp; TV</string>
    <string name="internet_dan_tv_cable_example">Contoh: *********</string>
    <string name="internet_dan_tv_cable">Internet &amp;\nCable TV</string>
    <string name="referral_successful">Referral code used successfully</string>
    <string name="session_expired_relogin">Your session has expired or cannot be renewed, please re-login</string>
    <string name="product_category_description">Production Category Description</string>
    <string name="select_category_title_s">Select Category %s</string>
    <string name="category_learn_more"><![CDATA[There has been a change in the category format.<font color="#0091ff"> Learn More</font>]]></string>
    <string name="free_for_x_trx"><![CDATA[<b>FREE</b> app fee for <b>%1$sx Bill or Pay!</b>]]></string>

    <string name="customer_bill">Customer Bill</string>
    <string name="discount">Discount</string>
    <string name="admin_fee_heading">What is Admin Fee?</string>
    <string name="admin_fee_subheading">Admin fee is a fee set by regulation to maintain product service quality.</string>
    <string name="show_saldo_cashback">Hooray! You get a cashback balance of %s</string>
    <!--    Vehicle Tax Strings-->
    <string name="policy_number">Policy Number</string>
    <string name="choose_policy">Choose the area according to your STNK province</string>
    <string name="select_vehicle_region">Select Samsat Region</string>
    <string name="pay_code">Pay Code</string>
    <string name="pay_code_example">Enter pay code</string>
    <string name="pay_code_hint">Make sure the payment code entered is correct</string>
    <string name="vehicle_tax_number_hint">An active mobile number is required for sending E-TBPKP</string>
    <string name="policy_number_example">Example: BB1234AAH</string>
    <string name="policy_number_hint">Make sure the police number is filled without spaces</string>
    <string name="machine_number">Machine number</string>
    <string name="machine_number_example">Example: JFZ2R464325</string>
    <string name="machine_number_hint">Make sure the vehicle engine number is filled without a dash (-)</string>
    <string name="frame_number">Frame Number/NIK/VIN</string>
    <string name="frame_number_example">Enter the 17 digit frame number</string>
    <string name="frame_number_hint">Make sure the frame number/NIK/VIN entered is correct</string>
    <string name="okay_got_it">Okay, Got it!</string>
    <string name="region_not_found">Region not found</string>
    <string name="search_again">Try searching for other keywords</string>
    <string name="vehicle_brand">Vehicle Brand</string>
    <string name="transportation_type">Transportation Type</string>
    <string name="vehicle_type">Vehicle Type</string>
    <string name="vehicle_colour">Vehicle Colour</string>
    <string name="vehicle_build_year">Vehicle Build Year</string>
    <string name="chassis_number">Chassis Number</string>
    <string name="pkb">PKB</string>
    <string name="view_all">View All</string>
    <string name="total_modal">Total Modal</string>
    <string name="untung">Untung</string>
    <string name="pribadi">Pribadi</string>
    <string name="pengeluaran_untuk">Pengeluaran untuk kebutuhan pribadi</string>

    <string name="why_trx_pending">Why is your payment pending?</string>
    <string name="network_problem_message">There may be a network problem on the bank or payment partner.</string>
    <string name="create_help_ticket">Create a Help Ticket</string>
    <string name="set_refund_method2">Where do you want your money back?</string>
    <string name="choose_refund_method">Choose an account or e-wallet for an automatic refund process</string>
    <string name="automatic_refund_message">If it fails, the money will be refunded automatically. Make sure you set up a return account.</string>
    <string name="to_learn">Learn</string>
    <string name="longer_pending_trx_message">Transactions are taking longer than they should. Learn more</string>
    <string name="invalid_referral_code">Wrong code entered</string>
    <string name="invite_friends_faster">Invite friends faster than your cellphone contacts</string>
    <string name="tnc_text_referral">Pelajari Undang Teman</string>
    <string name="send_referral">Share Referral</string>
    <string name="yes_continue">Yes, Continue</string>
    <string name="select_date_range">Select Date Range</string>

    <string name="failed_transaction_info">The payment failed and an automatic refund is in process. Please try again for re-payment.</string>
    <string name="expired_transaction_info">Payment expired. Please try again for re-transaction.</string>

    <string name="qris_is_processed_more">The QRIS application is being processed. More</string>
    <string name="qris_is_processed_more_bold_substring">More</string>
    <string name="qris_rejected_verify_again">QRIS submission failed. Please re-verify here.</string>
    <string name="qris_rejected_verify_again_bold_substring">re-verify here.</string>
    <string name="qris_setup_bank_here">Set up an account to receive QRIS payments here</string>
    <string name="qris_setup_bank_here_bold_substring">here</string>
    <string name="setup_account_for_qris">Set up an account first to use QRIS</string>
    <string name="select_account_to_receive_qris">Select an account to receive QRIS payments</string>

    <string name="payment_during_operating_hours">Payment during bank operating hours</string>
    <string name="payment_during_non_operating_hours">Payment outside bank operating hours</string>
    <string name="payment_forwarded_within_x">Payment will be forwarded to your account in about %1$s.</string>
    <string name="payment_non_op_forwarded_at_x">Payment %1$soutside bank operating hours %2$s will be forwarded to your account after %3$s.</string>
    <string name="time_am_w_timezone">%1$s %2$s AM</string>
    <string name="time_pm_w_timezone">%1$s %2$s PM</string>
    <string name="time_minutes">%1$s minutes</string>
    <string name="time_hour_minutes">%1$s hour %2$s minutes</string>

    <!--    Catalog Strings-->
    <string name="ubah_harga">Change Price</string>
    <string name="total_surcharge">Total surcharge </string>
    <string name="selling_price_definition">"Customer Bill + Admin Fee + profit = Selling Price to be paid by customer."</string>
    <string name="round_up_selling_price">Round up the selling price for easier payments</string>
    <string name="profit_margin_title">Set profit on each sale</string>
    <string name="total_additional_cost">The total additional cost is charged to the customer</string>
    <string name="stored">Stored</string>
    <string name="poster_promotions">Promotion Poster</string>
    <string name="ppob_set_sellling_price">Set Selling Price</string>
    <string name="change_in_capital_price">There is a change in the price of capital. Please go to the Catalog to change the selling price automatically.</string>
    <string name="catalog">Catalog</string>
    <string name="admin_fee_info">Fees set by regulation to maintain the best service quality.</string>

    <string name="save_favourite_subscriber">Save %s as new favorite subscriber</string>
    <string name="recommendation">Recommendation</string>
    <string name="show_more_recommendations">Show %s more favorites</string>

    <string name="create_catalog">Buat Katalog</string>
    <string name="preview_katalog">Catalog Preview</string>
    <string name="selling_price_filled">The selling price has been filled in completely</string>
    <string name="complete_setting_selling_price">The selling price is not complete.Come on, complete it first.</string>
    <string name="arrangement">Arrangement</string>
    <string name="promo_info">Please set the selling price first before downloading and sharing the price catalog.</string>
    <string name="catalog_poster_downloaded">Promotional poster has been successfully downloaded. Check the document file</string>
    <string name="catalog_poster_download_failed">Promotional poster download failed</string>

    <string name="detail">Detail</string>
    <string name="ticket_created_title">The obstacle you put forward has been successfully accepted by our system ✅</string>
    <string name="ticket_processing_message">We will process your problem in a maximum of 1x24 hours. We will contact you if we need additional data.</string>
    <string name="ticket_refund_message">Relax, your money is safe and we will refund if the transaction fails</string>
    <string name="ticket_network_issues_message">It took us some time to process due to network issues on the bank or payment partner.</string>
    <string name="view_ticket_help">View Tickets Help</string>
    <string name="ticket_created">Wait a minute, please! We are processing your report max. 1x24 hours. </string>
    <string name="ticket_failed">Help ticket failed to generate 😭.</string>
    <string name="transaction_succesful_ticket_closed">Hooray! Transaction successful .We have closed your support ticket.</string>

    <string name="saldo_limit_available">Max. additional balance %s</string>

    <string name="filter_history_all">All</string>
    <string name="filter_history_digital_product">Digital Products</string>
    <string name="filter_history_payment">Bill &amp; Pay</string>
    <string name="no_transaction">Well, no transactions yet</string>
    <string name="change_keyword">Change Keywords</string>
    <string name="no_transaction_description">Please make a transaction or use the filter to search for other transaction history.</string>
    <string name="no_search_transaction">Transaction history not found</string>
    <string name="no_search_transaction_description">Please change it with another keyword.</string>
    <string name="search_product_name">Search Product Name</string>
    <string name="search_customer_name">Search Csutomer Name</string>
    <string name="select_status">Select Status</string>
    <string name="select_product">Product</string>
    <string name="multi_product">Multi Product</string>
    <string name="multi_status">Multi Status</string>
    <string name="refund_successful">Refund Successful</string>
    <string name="qris_charging_note">You will be charged %1$s of the total bill \n<font color="#005DCC"><b>Read more</b></font></string>
    <string name="qris_charging_info">You will be charged QRIS %1$s of the total bill. QRIS fees or MDR (Merchant Discount Rate) are fees charged to users during transactions with QRIS.</string>
    <string name="money_in_expired_callback_info">If the customer\'s money has been deducted and the transaction status has expired, please wait 10–15 minutes, the status will change to successful, if it doesn\'t change, please contact CS</string>
    <string name="biaya_admin">Biaya Admin</string>
    <string name="saldo_bonus_diterima">Komisi Agen Diterima</string>
    <string name="total_cashback">Total cashback</string>
    <string name="cashback_yang">Cashback yang berhasil kamu terima.</string>
    <string name="riwayat_cashback">Riwayat Cashback</string>
    <string name="kamu_belum_ada_cashback">Kamu belum ada cashback bulan ini</string>
    <string name="yuk_lakukan_lebih">Yuk, lakukan lebih banyak transaksi dan bayar tagihan biar dapet cashback!</string>
    <string name="cashback_saldo">Cashback saldo %s akan dikirim %s</string>

    <string name="transactions_history">Transactions History</string>
    <string name="search_transactions">Search Transactions</string>
    <string name="transaction_history_coachmark_tab">Transaction history is easier to find</string>
    <string name="transaction_history_coachmark_tab_detail">Check your payment history by categories such as Digital Products, Bill &amp; Pay, Balance, and Komisi Agen. So neater!</string>

    <string name="transaction_history_coachmark_filter">Set transaction history search for more details</string>
    <string name="transaction_history_coachmark_filter_detail">Use filters to search for more specific transaction history by status, date, and product.</string>
    <string name="transaction_history_updated">Transaction history has been successfully updated</string>
    <string name="filter_removed">Filter successfully removed</string>
    <string name="no_filter_transaction">Search transactions with filters not found</string>
    <string name="no_filter_transaction_description">Please reduce the active filter or reset the filter.</string>
    <string name="result_count">Search result : <font color='#0091FF'>%d</font></string>
    <string name="sort_by">Sort By</string>
    <string name="upgrade">Upgrade</string>

    <string name="enter_the_nominal_topup_balance">Enter the nominal top up balance</string>
    <string name="must_verify_account_for_saldo">You must verify your account to use the BukuWarung Balance feature. Learn more</string>
    <string name="upgrade_kyc_account_to_increase_saldo_limit">Your BookWarung balance limit is %1$s. To continue, you must upgrade to a Priority Account. Learn more</string>
    <string name="your_saldo_limit_is">Top up your BukuWarung balance limit %1$s.</string>
    <string name="must_verify_account_for_payments">You must Verify Account to use the Bill and Pay feature. Learn more</string>
    <string name="upgrade_kyc_to_increase_daily_limit">Your daily transaction quota limit is %1$s. To continue, you must upgrade to a Priority Account. Learn more</string>
    <string name="upgrade_kyc_to_increase_trx_limit">Usage limits Pay max. %1$s. Please change the nominal or upgrade to a Priority Account. Learn</string>
    <string name="whitelisted_user_limit_reached">Usage limits Pay max. %1$s. Please change the nominal or upgrade to a Priority Account. Learn</string>
    <string name="maximum_x">Maximum %1$s</string>
    <string name="todays_payment_limit_is">Your daily limit remaining is %1$s</string>
    <string name="payment_detail">Payment Details</string>
    <string name="billing_detail">Billing Details</string>
    <string name="billing_will_be_recorded_after_success">Billing will be recorded automatically in the books after successful.</string>
    <string name="trx_will_be_recorded_after_success">Payment will be recorded automatically in the books after successful.</string>
    <string name="last_payments">Last Payments</string>
    <string name="check_all_payments">Check all payments</string>
    <string name="payment_code">Payment Code</string>
    <string name="payment_status">Payment Status</string>
    <string name="saldo_bonus_in">Komisi Agen In</string>
    <string name="saldo_bonus_out">Cashback Out</string>
    <string name="expiration_date">Expiration Date</string>
    <string name="cashback_will_be_credited_on">Cashback will be credited to Bukuwarung Saldo on %1$s.</string>

    <string name="faster_trx_using_fav">Your subscription? Add to favorites so that the next transaction is 2x faster.</string>

    <string name="postpaid_listrik_time_message">Payment cannot be made at 23.00–00.30 WIB based on PLN provisions</string>
    <string name="pln_meter">PLN Meter Number/Customer ID</string>
    <string name="pay_bpjs_upto">Bayar BPJS Hingga</string>
    <string name="bpjs_hint">11 – 16 digit card number</string>

    <string name="try_again_or_wait">Try again or wait for sometime.</string>
    <string name="make_credit_and_sales">Make credit sales and bills</string>
    <string name="incoming_saldo_refund">Incoming Saldo - Refund</string>
    <string name="saldo_refund">Saldo Refund</string>
    <string name="no_stock_yet">No stock yet</string>
    <string name="account_found">Account Found</string>
    <string name="account_already_used_error">The account is already used as a beneficiary account in another account. Please select another account.</string>
    <string name="account_blocked_error">For payment security, we lock this account. Please select another account. <b>Find out more.</b></string>
    <string name="account_blocked_error_clickable">Find out more.</string>
    <string name="account_name_matching_error">The name of the account holder does not match the registered ID card data. Please change to another account.</string>
    <string name="account_already_exist_error">The bank account has been added. Please select this account in your list of bank accounts.</string>
    <string name="wait_still_processing">Wait a minute, please! Checking process in progress</string>
    <string name="please_dont_close_util_finished">Please do not close the application until the checking process is complete.</string>
    <string name="saldo_bonus_x">Komisi Agen %1$s</string>
    <string name="plus_isi_saldo">+ Add Saldo</string>
    <string name="status_checking">Checking</string>
    <string name="yes_topup_saldo">Yes, Top Up</string>
    <string name="transaction_status">BELUM LUNAS</string>
    <string name="qris_bank_change_in_progress">Change the beneficiary\'s account to a <b>%1$s</b> is being processed. Temporarily, QRIS payments will go to the main beneficiary account. More</string>
    <string name="qris_bank_change_in_progress_clickable">More</string>
    <string name="qris_bank_change_rejected">Change beneficiary account to <b>%1$s</b> failed because <b>%2$s</b>. More</string>
    <string name="qris_bank_change_rejected_clickable">More</string>
    <string name="not_supported_info_text">Currently you cannot use this bank account as a beneficiary account. Please select another account.</string>
    <string name="bank_name_is_not_matching_ktp">The name of the account holder does not match the registered ID card data. Please select another account.</string>

    <string name="select_pdam_area">Select Customer PDAM Area</string>
    <string name="pdam_hint">4–20 digit customer number</string>

    <string name="payment_confirmation">Payment confirmation</string>
    <string name="type_of_payment">Type of payment</string>
    <string name="enter_selling_price">Enter Selling Price</string>
    <string name="info_msg">Payment will be recorded automatically in the books after successful.</string>
    <string name="make_sales">Make Sales</string>
    <string name="personal">Create Private</string>
    <string name="feature_under_development">Wow, this feature is under development</string>
    <string name="feature_under_development_body">For the time being, you can use other BukuWarung features first. We will inform you when this feature is ready to use.</string>
    <string name="rp_default_hint">301.000</string>

    <string name="add_saldo_balance">Fill in the balance to continue paying</string>

    <string name="train_ticket_title">Train Ticket Booking</string>
    <string name="loading_error_message">Wait, it is being fixed. The app will return to normal and you can try again.</string>
    <string name="data_lost_warning_message">The data you fill in will be deleted when the page exits.</string>
    <string name="exit_confirmation_message">Get off this page?</string>
    <string name="train_ticket_label">Train</string>
    <string name="passenger">Passenger</string>
    <string name="nik">NIK</string>
    <string name="nik_infant">No. KK</string>
    <string name="seat">Seat</string>
    <string name="detail_passenger">Detail Passenger</string>
    <string name="eticket_info">The e-ticket will be sent to the customer e-mail</string>
    <string name="order_details_train">Order details (For E-Ticket)</string>
    <string name="other_products_label">Other Products</string>
    <string name="e_ticket_download_success">E-Ticket successfully downloaded. Check document files</string>
    <string name="kirim">Share</string>
    <string name="take_photo">Take a photo</string>
    <string name="confirm_store_location">Confirm Store Location</string>
    <string name="payment_after_verification">Your payment will enter your account after successful photo verification.</string>
    <string name="photo_under_verification">Photos of shops and products under verification max. %d working days.</string>
    <string name="payment_hold_until_verification">For security reasons, your payment will not be able to enter your account until you have successfully verified the store.</string>
    <string name="choose_location">Choose Location</string>
    <string name="shop_verification">Shop verification</string>
    <string name="adress_error">Error in fetching address</string>
    <string name="update_now">Update Now</string>
    <string name="app_update_message">Come on, update BukuWarung to the latest version so that using the application will be smoother!</string>
    <string name="app_update_title">It\'s time to update the BukuWarung Juragan app 🤩</string>
    <string name="repeat_store_verification">Repeat store verification</string>
    <string name="repeat_account_verification">Repeat account verification</string>

    <string name="name_matching_error">The bank account cannot be added because the name of the bank account is different from the name registered in BukuWarung. Do you think the name is appropriate? Please verify here.</string>
    <string name="name_matching_error_qris">The bank account cannot be added because the name of the bank account is different from the name registered in BukuWarung. Do you think the name is appropriate? Please verify.</string>
    <string name="name_matching_in_progress">Verify your bank account is being processed max. 7 working days. learn</string>
    <string name="name_matching_failed">The bank account verification failed because the bank account name was different from the name registered in BukuWarung. Please select another account. learn</string>
    <string name="matching_appeal_document_submitted">Yey! The bank account verification document has been sent successfully and will be processed max. 7 working days.</string>

    <string name="new_lable_2">Baru!</string>
    <string name="bnpl_transaction_fees">BNPL Admin Fee</string>
    <string name="saldo_talangin_dulu">Saldo Talangin Dulu</string>
    <string name="daftar_sekarang">Daftar Sekarang</string>
    <string name="want_capital">Mau modal s.d Rp 2 juta?</string>
    <string name="lets_try">Let\'s try</string>
    <string name="bnpl_remaining_limit">Remaining Limit: %s</string>
    <string name="new_caps">NEW</string>

    <string name="payment_timer_limit">Payment Limit</string>
    <string name="payment_expiry_title">Well, payment expired</string>
    <string name="payment_expiry_subtitle">Your payment deadline has expired. Please make a repeat purchase.</string>
    <string name="download_valid_application">Please download any application which can view pdf.</string>

    <string name="printer_text">Printer</string>
    <string name="add_supplier">Add Supplier</string>
    <string name="check_number">Cek Nomor</string>
    <string name="check_registered_number_hint">*periksa nomor supplier yang sudah terdaftar di Tokoko</string>
    <string name="enter_shop_name">Masukan nama toko</string>
    <string name="shop_link">Link Toko</string>
    <string name="permission_import_contact">Beri izin impor kontak HP kamu</string>
    <string name="add_supplier_number">Tambah Nomor HP Supplier lebih mudah</string>
    <string name="safe_and_private_data">Data dan privasi kamu dijamin aman</string>
    <string name="supplier_not_registered">Nomor supplier belum terdaftar di Tokoko.\nNama supplier ini bisa berubah sesuai nama toko yang didaftarkan supplier.</string>
    <string name="supplier_registered">Nomor supplier sudah terdaftar di Tokoko</string>

    <string name ="other_payment_methods">Other Payment Methods</string>
    <string name ="bank_virtual_account">Bank Virtual Account</string>
    <string name ="subtitle_payment_method">Has a minimum transaction limit</string>
    <string name ="saldo_monthly_limit_reached">You have reached your monthly usage limit</string>
    <string name ="saldo_daily_limit_reached">You have reached your daily usage limit</string>
    <string name ="saldo_advertisement">⚡ Paying using a balance is 3x faster!</string>
    <string name ="saldo_selected_advertisement">Yey! Payouts will be 3x faster ⚡</string>
    <string name ="saldo_balance_error">Fill in the balance to continue payment</string>
    <string name="saldo_reward_disabled">Total Bonus Balance %s.\n Only valid for the BukuWarung Balance payment method</string>
    <string name="use_saldo_reward">Use %s Komisi Agen</string>
    <string name="total_saldo_bonus">Total Komisi Agen %s</string>
    <string name="saldo_bonus">Komisi Agen</string>
    <string name="saldo_bonus_used">Komisi Agen Used</string>
    <string name="pay_using_saldo_bonus">Pay more sparingly using Bonus Balance! 🎁</string>
    <string name="apply_now">Apply Now</string>
    <string name="expiring_cashback_info">Your Bonus balance of %1$s will expire at %2$s. Read more</string>
    <string name="expiring_cashback_info_clickable">Read more</string>

    <string name="discount_level">Discount Level %s</string>
    <string name="train_loading_title">Please wait! Do not close the page until the process is complete</string>
    <string name="train_loading_message">Meanwhile, we are diverting the order process from KAI to BukuWarung</string>
    <string name="powered_by">Powered By</string>
    <string name="failed_to_load_page">Failed to load page</string>
    <string name="please_reorder">Please reorder</string>
    <string name="return_to_home">Return to Home</string>

    <string name="data_restore_success_title">Happy! Data updated successfully!</string>
    <string name="data_restore_error_title">Data failed to update</string>
    <string name="data_restore_error_desc">Please wait and try again later, OK?</string>
    <string name="no_internet_title">Please wait and try again later, OK?</string>
    <string name="no_internet_desc">Make sure your internet connection is connected before using this feature.</string>

<!--    BottomSheet-->
    <string name="feature_requires_internet_connection">Feature requires internet connection</string>
    <string name="want_to_quit_app">Quit app?</string>
    <string name="makes_sure_you_are_connected_to_save_records">Make sure you are connected to an internet connection first,so that your records in offline mode are saved.</string>
    <string name="data_is_backed_up_can_logout">After logging out, you must log in again to use the BukuWarung application.</string>
    <string name="sync_inprogress_logout_would_delete_data">Logging data is being synced. If you log out, then your recordings in offline mode will be deleted.</string>
    <string name="cannot_exit_application">Cannot exit application</string>

    <string name="start_kyb_message"><![CDATA[Yeah, upgrade to Priority Account to use this feature again. <b><font color="#E50707">Read more</color></b> ]]></string>
    <string name="remaining_limit_x">Remaining Limit %1$s</string>
    <string name="coming_soon">Coming Soon</string>

    <string name="nominal_topup">Nominal Top Up</string>
    <string name="contact_number">Contact Number</string>
    <string name="installment_product">Installment Product</string>
    <string name="total_kwh">Total kWh</string>

    <string name="saldo_balance_limit_details">Saldo Balance Limit Details</string>
    <string name="saldo_balance_limits_message">Following are the daily limits, monthly limits, and the maximum balance for each account level:</string>
    <string name="advanced_saldo_limits">Premium PLUS Account Limits</string>
    <string name="daily_limit">Daily Limit</string>
    <string name="monthly_limit">Monthly Limit</string>
    <string name="saldo_hold_limit">Max Saldo Hold Limit</string>
    <string name="supreme_saldo_limits">Supreme Account Limit</string>
    <string name="saldo_limits_info">Remaining daily limit: %1$s\nRemaining monthly limit: %2$s</string>
    <string name="remaining_daily_saldo_limit_is">The daily limit for using the remaining balance is %1$s</string>
    <string name="remaining_monthly_saldo_limit_is">The monthly limit for using the remaining balance is %1$s</string>
    <string name="x_billion">%1$s Billion</string>
    <string name="x_million">%1$s Million</string>

    <string name="standard">Standard</string>
    <string name="premium">Premium</string>
    <string name="prioritas">Prioritas</string>

    <string name="x_other_item">%1$d Other Items</string>
    <string name="click_details">Check details</string>
    <string name="saldo">Saldo</string>
    <string name="money_refund">Money Refund</string>

    <string name="destination_account">Destination account</string>
    <string name="add_to_fav">Add to Favorites</string>
    <string name="total_received_by_customer">Total Received by Customers</string>
    <string name="payment_out_info">Pembayaran akan tercatat otomatis di Pembukuan setelah pembayaran berhasil.</string>
    <string name="new_payment_out_bank_title">Payment to a new account</string>
    <string name="new_payment_out_bank_subtitle">Click to select the destination bank</string>
    <string name="menu_item_remove_from_fav">Remove from Favorites</string>
    <string name="find_bank_account">Find a bank account</string>
    <string name="select_bank">Select Bank</string>
    <string name="account">Account</string>
    <string name="banks_not_found">Well, the bank we are looking for is not found</string>

    <string name="no_fav_account">Wow, no favorite bank account yet</string>
    <string name="no_fav_account_subtitle">Click \'star\' to make your favorite bank account so that the next payment is faster</string>
    <string name="success_fav_message">Favorite customer added successfully</string>
    <string name="remove_fav_message">Favorite subscriber removed</string>
    <string name="payment_out_status_pending_title">Payment is being processed</string>
    <string name="payment_out_status_pending_subtitle">Your payment is being forwarded to the customer</string>
    <string name="payment_out_status_failed_title">Payment Failed</string>
    <string name="payment_out_status_failed_subtitle">Your refund will be processed immediately</string>
    <string name="payment_out_status_success_title">Hooray! Payment successful</string>
    <string name="ppob_status_success_title">Hooray! Payment [%s] successful</string>
    <string name="payment_out_status_success_subtitle">Your payment has been received by the customer</string>
    <string name="payment_out_destination_account">Destination Account</string>
    <string name="view_payment_details">View Payment Details</string>
    <string name="view_payment_details_with_time">View Payment Details (in %s seconds)</string>

    <string name="add_account">Add New Account</string>
    <string name="remove_account_message">Bank account deleted successfully</string>
    <string name="search_account_subtitle">Come on, make your first payment to a customer starting with adding a new account.</string>
    <string name="use_saldo_info">Now you can pay using Balance! Faster process⚡</string>
    <string name="qris_deactivated">QRIS Deactivated</string>
    <string name="continue_this_payment">Continue this payment?</string>
    <string name="make_new">Buat Baru</string>
    <string name="freeform_ewallet_admin_fees">An admin fee adjustment will be applied at the end of the payment</string>
    <string name="start_inviting_friends">Yuk, mulai undang teman!</string>
    <string name="invited_friends"> Teman Yang Diundang</string>
    <string name="send_sms_without_saving_contact">You can send proof of transactions without the need to save customer numbers.</string>
    <string name="send_to">Send to %s</string>
    <string name="phone_number_for_sending_message">The cellphone number is used to send proof of transactions via WhatsApp or SMS faster.</string>
    <string name="sms_stop_message">Please click send to share the code with the customer. Currently BukuWarung is no longer sending codes via automatic SMS.</string>

    <string name="saldo_freeze_message">The balance cannot be used yet. Please wait or use another payment method. learn</string>
    <string name="saldo_freeze_message_highlight_text">Learn</string>
    <string name="saldo_freeze_title">You can\'t use your balance yet, OK?</string>
    <string name="saldo_freeze_subtitle">For security reasons, the balance feature can be used after requesting access permission from the BukuWarung support team.</string>
    <string name="no_trx_in_last_30_days">No payments in the last 30 days</string>
    <string name="no_recent_trx_hint">Come on, make payments or purchase PPOB products to get benefits right now!</string>
    <string name="access_denied">Access Denied</string>
    <string name="security_check_failed">This device failed all security checks. This may be due to the following reasons\n\n
        * Device is rooted\n
        * App is not installed from Play Store\n\n
        Please install the app from Play Store and on a genuine Android device.</string>
    <string name="buku_modal_entry">Buku Modal</string>
    <string name="learn_and_apply">Pelajari &amp; Ajukan</string>
    <string name="regular_products">Regular Products</string>
    <string name="promotional_products">Products currently on promotion</string>
    <string name="mobilisten_notification_channel_name">BukuWarung</string>
    <string name="subscription">Subscription</string>
</resources>
