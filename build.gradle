import com.bukuwarung.buildsrc.Libs

buildscript {
    ext.buildConfig = [
            'compileSdk' : 34,
            'minSdk'     : 21,
            'targetSdk'  : 34,
            "versionCode": 5387,
            "versionName": "3.87.0"
    ]

    repositories {
        google()
//        jcenter()
        mavenCentral()
        gradlePluginPortal()
        maven { url "https://jitpack.io" }
        flatDir {
            dirs 'libs'
        }
        maven { url "https://oss.sonatype.org/content/repositories/snapshots" }
        maven { url "https://repo1.maven.org/maven2" }
    }
    dependencies {
        classpath Libs.Google.Firebase.crashlyticsGradle
        classpath Libs.androidGradlePlugin
        classpath Libs.Google.googleServices
        classpath Libs.JetBrains.Kotlin.gradlePlugin
        classpath Libs.AndroidX.Navigation.navigationSafeArgs
        classpath Libs.androidPerfDependency
        classpath Libs.AndroidX.Hilt.hiltPlugin
    }
}

allprojects {
    repositories {
        google()
//        jcenter()
        mavenCentral()
        maven { url "https://jitpack.io" }
        maven { url 'https://repo.survicate.com' }
        maven { url 'https://maven.zohodl.com'}
    }
}


task clean(type: Delete) {
    delete rootProject.buildDir
}
